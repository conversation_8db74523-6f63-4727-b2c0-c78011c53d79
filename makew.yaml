assets:
  ./dev_kek.json: /opt/dev_kek.json

ci_envars:
  CGO_ENABLED: "1"
  GOTESTFLAGS: -tags integration_test
  GO_VERSION: 1.24-ci
  MONGO_TEST_VERSION: 4.2.12
  MONGO_TEST_PORT: "27017"
  REDIS_TEST_PORT: "6379"
  ENABLE_MONGO_TRANSACTION: "true"
  MONGO_INITDB_ROOT_USERNAME: test_user
  MONGO_INITDB_ROOT_PASSWORD: test_password
  REP_CONNECTION_URI: amqp://guest:guest@rabbitmq:35672/test
  REDIS_TEST_HOST: redis
  MONGO_TEST_HOST: mongodb
  VOS_ENDPOINT: http://minio:9000
  VOS_INTERNAL_ENDPOINT: http://minio:9000
  MINIO_ACCESS_KEY: admin
  MINIO_SECRET_KEY: password
  LM_PROJECT_NAME: driver

ci_services:
  - SERVICE_MONGODB_4
  - SERVICE_REDIS
  - SERVICE_MINIO
  - SERVICE_RABBITMQ

build_image: "harbor.linecorp.com/lineman-infra/golang:1.24-ci"

build_tags:
  - "netgo"

docker_image_name: harbor.linecorp.com/lineman/fleet-distribution

ceylon_project_path: lineman/driver/fleet-distribution

lineman_deploy:
  - project_name: ["fleet-distribution"]

deploy_tool: DEPLOYMENT_TOOL_CEYLON

tools:
  - name: "mockgen"
    install: "go install github.com/golang/mock/mockgen@v1.6.0"
  - name: "providergen"
    install: "go install git.wndv.co/go/providergen@v1.3.0"
