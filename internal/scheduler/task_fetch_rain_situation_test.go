package scheduler_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	mock_asynq_client "git.wndv.co/lineman/fleet-distribution/internal/asynqclient/mock_asynqclient"
	"git.wndv.co/lineman/fleet-distribution/internal/asynqtask"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/scheduler"
)

func TestFetchRainSituation_ProcessTask(t *testing.T) {
	t.Run("do nothing when disable flag", func(tt *testing.T) {
		task, _, cleanup := newTaskFetchRainSituationTest(tt, false)
		defer cleanup()
		ctx := context.Background()

		ta, _ := asynqtask.NewFetchRainSituationTask()
		err := task.ProcessTask(ctx, ta)

		assert.NoError(tt, err)
	})

	t.Run("happy case", func(tt *testing.T) {
		task, deps, cleanup := newTaskFetchRainSituationTest(tt, true)
		defer cleanup()
		ctx := context.Background()

		ta, _ := asynqtask.NewFetchRainSituationTask()
		deps.rainSituationService.EXPECT().Fetch(ctx).Return(service.FetchResult{})

		err := task.ProcessTask(ctx, ta)

		assert.NoError(tt, err)
	})

}

type taskFetchRainSituationDeps struct {
	rainSituationService *mock_service.MockRainSituationService
	cfg                  scheduler.TaskFetchRainSituationConfig
	client               *mock_asynq_client.MockAsynqClient
}

func newTaskFetchRainSituationTest(tt *testing.T, enableFlag bool) (*scheduler.TaskFetchRainSituation, *taskFetchRainSituationDeps, func()) {
	ctrl := gomock.NewController(tt)

	deps := &taskFetchRainSituationDeps{
		rainSituationService: mock_service.NewMockRainSituationService(ctrl),
		cfg: scheduler.TaskFetchRainSituationConfig{
			IsTaskFetchRainSituationEnabled: enableFlag,
		},
		client: &mock_asynq_client.MockAsynqClient{},
	}

	task := scheduler.NewTaskFetchRainSituation(deps.rainSituationService, deps.cfg, deps.client)
	return task, deps, func() { ctrl.Finish() }

}
