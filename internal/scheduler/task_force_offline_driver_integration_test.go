//go:build integration_test
// +build integration_test

package scheduler_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/account"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestTaskForceOfflineDriver(t *testing.T) {
	driverID := "DRV_ONLINE_2"
	ctn := ittest.NewContainer(t)
	ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_force_driver_offline")

	gctx := testutil.NewContextWithRecorder()
	gctx.
		SetPUT("/v1/account/status").
		Authorized(ctn.RedisTokenStore, driverID).
		Body().JSON(account.UpdateDriverStatusRequest{Status: model.StatusOnline}).
		Build()
	ctn.AccountAPI.AccountConfig.MinimumCreditRequired = types.Money(0)

	ginCtx := gctx.GinCtx()
	wg := safe.CreateWaitGroupOnGctx(ginCtx)
	ctn.GinEngineRouter.HandleContext(ginCtx)
	wg.Wait()

	<-time.After(110 * time.Millisecond) // wait for more than 100ms (DriverLastAttemptTTL)

	ctx := context.Background()
	ctn.TaskForceOfflineDriver.Cfg.DriverLastAttemptTTL = 100 * time.Millisecond
	ctn.TaskForceOfflineDriver.Execute(ctx)
	drv, err := ctn.DriverRepository.FindDriverID(ctx, driverID)
	require.NoError(t, err)
	require.Equal(t, model.StatusOffline, drv.Status)
}
