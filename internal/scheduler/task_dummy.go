package scheduler

import (
	"context"
	"fmt"
	"time"

	"github.com/hibiken/asynq"
	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/asynqtask"
)

type TaskDummy struct {
	cfg    TaskDummyConfig
	client *asynq.Client
}

type TaskDummyConfig struct {
	TaskDummyInterval  time.Duration `envconfig:"TASK_DUMMY_INTERVAL" default:"1m"`
	TaskDummyEnabled   bool          `envconfig:"TASK_DUMMY_ENABLED" default:"false"`
	TaskDummyRetention time.Duration `envconfig:"TASK_DUMMY_RETENTION_HR" default:"48h"`
}

func ProvideTaskDummyConfig() (cfg TaskDummyConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

func NewTaskDummy(cfg TaskDummyConfig, client *asynq.Client) *TaskDummy {
	return &TaskDummy{cfg: cfg, client: client}
}

func ProvideTaskDummy(scheduler *Scheduler, cfg TaskDummyConfig, client *asynq.Client) *TaskDummy {
	task := NewTaskDummy(cfg, client)

	if err := scheduler.Run("task_dummy", task.Execute, cfg.TaskDummyInterval); err != nil {
		panic("cannot execute dummy task")
	}

	return task
}

func (td *TaskDummy) Execute(ctx context.Context) {
	if !td.cfg.TaskDummyEnabled {
		logx.Info().Context(ctx).
			Str("task", "TaskDummy").
			Msg("task_dummy is disabled.")
		return
	}

	if td.client == nil {
		return
	}

	t1, err := asynqtask.NewDummyTask()
	if err != nil {
		logx.Error().Context(ctx).
			Err(err).
			Str("task", "TaskDummy").
			Msg("fail to create task")
		return
	}

	_, err = td.client.Enqueue(t1, asynq.Retention(td.cfg.TaskDummyRetention))
	if err != nil {
		logx.Error().Context(ctx).
			Err(err).
			Str("task", "TaskDummy").
			Msg("fail to enqueue task")
	}
}

// ProcessTask implements asynq.Handler.
func (*TaskDummy) ProcessTask(context.Context, *asynq.Task) error {
	fmt.Printf("this is dummy task from process task - time: %v\n", time.Now())
	return nil
}
