// Code generated by MockGen. DO NOT EDIT.
// Source: task_throttle_job_controller.go

// Package mock_scheduler is a generated GoMock package.
package mock_scheduler

import (
	context "context"
	reflect "reflect"
	time "time"

	scheduler "git.wndv.co/lineman/fleet-distribution/internal/scheduler"
	gocron "github.com/go-co-op/gocron"
	gomock "github.com/golang/mock/gomock"
)

// MockThrottleScheduler is a mock of ThrottleScheduler interface.
type MockThrottleScheduler struct {
	ctrl     *gomock.Controller
	recorder *MockThrottleSchedulerMockRecorder
}

// MockThrottleSchedulerMockRecorder is the mock recorder for MockThrottleScheduler.
type MockThrottleSchedulerMockRecorder struct {
	mock *MockThrottleScheduler
}

// NewMockThrottleScheduler creates a new mock instance.
func NewMockThrottleScheduler(ctrl *gomock.Controller) *MockThrottleScheduler {
	mock := &MockThrottleScheduler{ctrl: ctrl}
	mock.recorder = &MockThrottleSchedulerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockThrottleScheduler) EXPECT() *MockThrottleSchedulerMockRecorder {
	return m.recorder
}

// FindJobsByTag mocks base method.
func (m *MockThrottleScheduler) FindJobsByTag(tags ...string) ([]*gocron.Job, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range tags {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindJobsByTag", varargs...)
	ret0, _ := ret[0].([]*gocron.Job)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindJobsByTag indicates an expected call of FindJobsByTag.
func (mr *MockThrottleSchedulerMockRecorder) FindJobsByTag(tags ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindJobsByTag", reflect.TypeOf((*MockThrottleScheduler)(nil).FindJobsByTag), tags...)
}

// Run mocks base method.
func (m *MockThrottleScheduler) Run(name string, task func(context.Context), every time.Duration, options ...scheduler.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{name, task, every}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Run", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Run indicates an expected call of Run.
func (mr *MockThrottleSchedulerMockRecorder) Run(name, task, every interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{name, task, every}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Run", reflect.TypeOf((*MockThrottleScheduler)(nil).Run), varargs...)
}

// UpdateEvery mocks base method.
func (m *MockThrottleScheduler) UpdateEvery(name string, every time.Duration, options ...scheduler.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{name, every}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateEvery", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateEvery indicates an expected call of UpdateEvery.
func (mr *MockThrottleSchedulerMockRecorder) UpdateEvery(name, every interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{name, every}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEvery", reflect.TypeOf((*MockThrottleScheduler)(nil).UpdateEvery), varargs...)
}
