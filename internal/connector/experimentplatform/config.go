package experimentplatform

import (
	"strings"
	"sync"

	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
)

type ExperimentPlatformConfig struct {
	URL string `envconfig:"EXPERIMENT_PLATFORM_URL" default:""`
}

func newDefaultExperimentPlatformConfig() *ExperimentPlatformConfig {
	cfg := new(ExperimentPlatformConfig)
	envconfig.MustProcess("", cfg)
	return cfg
}

type DistributionExperimentPlatformDbConfig struct {
	SceneID  string   `envconfig:"SWITCHBACK_EXP_DISTRIBUTION_SCENE_ID" default:""`
	LayerIDs []string `envconfig:"SWITCHBACK_EXP_DISTRIBUTION_LAYER_IDS" default:""`
}

type AtomicDistributionExperimentPlatformDbConfig struct {
	lock   sync.RWMutex
	Config DistributionExperimentPlatformDbConfig
	config.Validatable[DistributionExperimentPlatformDbConfig]
}

func (cfg *AtomicDistributionExperimentPlatformDbConfig) Parse() {
	cfg.lock.Lock()
	defer cfg.lock.Unlock()
	envconfig.MustProcess("", &cfg.Config)

	layerIDs := []string{}
	for _, id := range cfg.Config.LayerIDs {
		layerIDs = append(layerIDs, strings.TrimSpace(id))
	}
	cfg.Config.LayerIDs = layerIDs
}

func (cfg *AtomicDistributionExperimentPlatformDbConfig) Get() DistributionExperimentPlatformDbConfig {
	cfg.lock.RLock()
	defer cfg.lock.RUnlock()
	return cfg.Config
}

func ProvideDistributionExperimentPlatformDbConfig(configUpdater *config.DBConfigUpdater) *AtomicDistributionExperimentPlatformDbConfig {
	var cfg AtomicDistributionExperimentPlatformDbConfig
	configUpdater.Register(&cfg)
	return &cfg
}
