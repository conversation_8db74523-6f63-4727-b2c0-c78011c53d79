package bulk

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"net/http"
	"slices"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"git.wndv.co/go/logx/v2"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var DataStoreIsNotCreateType = errors.New("DataStore is not DataStoreWithValidateCreate")

var DataStoreIsNotDeleteType = errors.New("DataStore is not DataStoreWithValidateDelete")

type UpdaterFunc func(key string, data map[string]interface{}) bson.M

//go:generate mockgen -destination=mock/mock_operation_service.go -package=mock_bulk git.wndv.co/lineman/fleet-distribution/internal/bulk OperationService
type OperationService interface {
	// ValidateFromDS validate data from data store used in update operation
	ValidateFromDS(ctx context.Context, ds mongodb.DataStoreInterface, data map[string]map[string]interface{}) (map[string]map[string]interface{}, *Result, []model.AuditLog, error)
	// ValidateDeletionFromDS validate data from data store used in delete operation
	ValidateDeletionFromDS(ctx context.Context, ds mongodb.DataStoreInterface, keys []string) (map[string]map[string]interface{}, *Result, []model.AuditLog, error)
	// ValidateCreationFromDS validate data from data store used in create operation
	ValidateCreationFromDS(ctx context.Context, ds mongodb.DataStoreInterface, data map[string]map[string]interface{}) (map[string]map[string]interface{}, *Result, []model.AuditLog, error)
	// Operate run the operation used by updater, creator, deleter
	Operate(ctx context.Context, ds mongodb.DataStoreInterface, data map[string]map[string]interface{}, operation SingleCollectionOperation, auditLog []model.AuditLog) *Result
	// CreateFunc create insert one model
	CreateFunc(data map[string]interface{}) *mongo.InsertOneModel
	// DeleteFunc create delete one model
	DeleteFunc(key string, id string) *mongo.DeleteOneModel
	// UpdaterFunc create update one model
	UpdaterFunc(ctx context.Context, query bson.M) *mongo.UpdateOneModel
	// ExportOperate run the operation for exporter
	ExportOperate(headers []string, result []map[string]string) (*[]byte, error)
	// WriteToCSV write data to csv
	WriteToCSV(gctx *gin.Context, filename string, data *[]byte) error
}

type operationServiceImpl struct {
	cfg          *Config
	auditLogRepo repository.AuditLogRepository
}

func ProvideOperationService(cfg *Config, auditLog repository.AuditLogRepository) OperationService {
	return &operationServiceImpl{
		cfg:          cfg,
		auditLogRepo: auditLog,
	}
}

func (o *operationServiceImpl) ValidateFromDS(ctx context.Context, ds mongodb.DataStoreInterface, data map[string]map[string]interface{}) (map[string]map[string]interface{}, *Result, []model.AuditLog, error) {
	dsConfig := o.cfg.GetDataSourceConfig(ds)
	result, auditLog, err := dsConfig.Validate(ctx, data)
	if err != nil {
		return nil, nil, nil, err
	}

	newData := map[string]map[string]interface{}{}
	for key, value := range data {
		if slices.Contains(result.Successes, key) {
			newData[key] = value
		}
	}
	return newData, result, auditLog, nil
}

func (o *operationServiceImpl) ValidateDeletionFromDS(ctx context.Context, ds mongodb.DataStoreInterface, keys []string) (map[string]map[string]interface{}, *Result, []model.AuditLog, error) {
	dsConfig := o.cfg.GetDataSourceConfig(ds)
	dataStore, isok := dsConfig.(DataStoreWithValidateDelete)
	if !isok {
		return nil, nil, nil, DataStoreIsNotDeleteType
	}
	result, auditLogs, err := dataStore.ValidateDelete(ctx, keys)
	if err != nil {
		return nil, nil, nil, err
	}
	newData := map[string]map[string]interface{}{}
	for _, key := range keys {
		if slices.Contains(result.Successes, key) {
			newData[key] = map[string]interface{}{}
		}
	}

	return newData, result, auditLogs, nil
}

func (o *operationServiceImpl) ValidateCreationFromDS(ctx context.Context, ds mongodb.DataStoreInterface, data map[string]map[string]interface{}) (map[string]map[string]interface{}, *Result, []model.AuditLog, error) {
	dsConfig := o.cfg.GetDataSourceConfig(ds)
	dataStore, isok := dsConfig.(DataStoreWithValidateCreate)
	if !isok {
		return data, nil, nil, DataStoreIsNotCreateType
	}
	result, auditLogs, err := dataStore.ValidateCreate(ctx, data)
	if err != nil {
		return nil, nil, nil, err
	}
	newData := map[string]map[string]interface{}{}
	for key, value := range data {
		if slices.Contains(result.Successes, key) {
			newData[key] = value
		}
	}
	return newData, result, auditLogs, nil
}

func (o *operationServiceImpl) Operate(ctx context.Context, ds mongodb.DataStoreInterface, data map[string]map[string]interface{}, operation SingleCollectionOperation, auditLog []model.AuditLog) *Result {
	// Exporter is not allow in the Operate
	if _, ok := operation.(SingleCollectionOperationExporter); ok {
		return &Result{}
	}
	resp := &Result{}
	models := make([]mongo.WriteModel, 0, len(data))
	// TODO: We can optimize bulk write to group id of rider when update same value
	for id, raw := range data {
		cloned := cloneMap(raw)
		switch operation.(type) {
		case SingleCollectionOperationUpdater:
			query := operation.(SingleCollectionOperationUpdater).Updater(id, cloned)
			updater := o.UpdaterFunc(ctx, query).SetFilter(o.cfg.GetDataStoreFilters(ds, id))
			models = append(models, updater)
		case SingleCollectionOperationCreator:
			models = append(models, o.CreateFunc(cloned))
		case SingleCollectionOperationDeleter:
			models = append(models, o.DeleteFunc(o.cfg.GetKeyFromDS(ds), id))
		}
	}

	batchSize := operation.GetBatchSize()
	if batchSize < 1 {
		batchSize = o.cfg.BatchSize
	}

	delay := operation.GetBatchDelay()
	if delay < 1 {
		delay = o.cfg.DelayTime
	}

	logx.Info().Context(ctx).Msgf("[%s] processing batch_size: %d, delay_ms: %d", operation.Type(), batchSize, delay.Milliseconds())

	chunks := lo.Chunk(models, batchSize)
	for _, chunk := range chunks {
		_, err := ds.BulkWrite(ctx, chunk, options.BulkWrite().SetOrdered(false))
		driverIDs := o.getIDFromBulkWriteModel(ds, chunk)
		if err != nil {
			resp.AddManyFailure(driverIDs, err.Error())
		} else {
			resp.AddManySuccess(driverIDs)
		}
		time.Sleep(delay)
	}

	if auditLog != nil && len(auditLog) > 0 {
		resultLog := make([]*model.AuditLog, 0)
		for idx := range auditLog {
			if slices.Contains(resp.Successes, auditLog[idx].Object.ID) {
				resultLog = append(resultLog, &auditLog[idx])
			}
		}
		if len(resultLog) > 0 {
			audiChunks := lo.Chunk(resultLog, batchSize)
			for _, chunk := range audiChunks {
				if err := o.auditLogRepo.InsertMany(ctx, chunk); err != nil {
					resp.AddManyFailure(resp.Successes, err.Error())
				}
			}

		}
	}

	return resp
}

func (o *operationServiceImpl) getIDFromBulkWriteModel(ds mongodb.DataStoreInterface, models []mongo.WriteModel) []string {
	ids := make([]string, len(models))
	key := o.cfg.GetKeyFromDS(ds)
	for i, m := range models {
		switch m.(type) {
		case *mongo.InsertOneModel:
			ids[i] = m.(*mongo.InsertOneModel).Document.(map[string]interface{})[key].(string)
		case *mongo.UpdateOneModel:
			driverID := m.(*mongo.UpdateOneModel).Filter.(bson.M)[key].(string)
			ids[i] = driverID
		case *mongo.DeleteOneModel:
			driverID := m.(*mongo.DeleteOneModel).Filter.(bson.M)[key].(bson.M)["$in"].([]string)[0]
			ids[i] = driverID
		}

	}
	return ids
}

func (o *operationServiceImpl) CreateFunc(data map[string]interface{}) *mongo.InsertOneModel {
	return mongo.NewInsertOneModel().SetDocument(data)
}

func (o *operationServiceImpl) DeleteFunc(key string, ids string) *mongo.DeleteOneModel {
	return mongo.NewDeleteOneModel().SetFilter(bson.M{key: bson.M{"$in": []string{ids}}})
}

// TODO: Not sure if we need this in generic updater
func (o *operationServiceImpl) UpdaterFunc(ctx context.Context, query bson.M) *mongo.UpdateOneModel {
	n := timeutil.GetTimeFromContext(ctx)
	if _, exists := query["$set"]; !exists {
		query["$set"] = map[string]interface{}{}
	}
	query["$set"].(map[string]interface{})["updated_at"] = n
	return mongo.NewUpdateOneModel().SetUpdate(query)
}

func (o *operationServiceImpl) ExportOperate(headers []string, result []map[string]string) (*[]byte, error) {
	b := new(bytes.Buffer)
	writer := csv.NewWriter(b)
	if len(result) > 0 {
		err := writer.Write(headers)
		if err != nil {
			return nil, errors.Wrap(err, "Failed to write to csv")
		}
		for _, data := range result {
			values := []string{}
			for _, key := range headers {
				values = append(values, data[key])
			}
			err := writer.Write(values)
			if err != nil {
				return nil, errors.Wrap(err, "Failed to write to csv")
			}
		}
	}

	writer.Flush()

	res := b.Bytes()
	return &res, nil
}

func (o *operationServiceImpl) WriteToCSV(gctx *gin.Context, filename string, data *[]byte) error {
	if data == nil {
		return errors.New("Data is nil")
	}
	if filename == "" {
		filename = "export.csv"
	}
	gctx.Writer.Header().Set("content-disposition", fmt.Sprintf("attachment;filename=%s", filename))
	gctx.Data(http.StatusOK, "text/csv", *data)
	return nil
}

func cloneMap(src map[string]interface{}) map[string]interface{} {
	dest := make(map[string]interface{})
	for k, v := range src {
		dest[k] = v
	}
	return dest
}
