[{"_id": {"$oid": "66c2f0bea50f416be1049e85"}, "metadata": {}, "quote_id": "LMTQ-69595", "user_id": "U463375455441567", "service_type": "bike", "price_scheme": "", "autostart": false, "routes": [{"id": "", "name": "อู๋", "address": "918 Sukhumvit Road, Khwaeng Phra Khanong, Khet Khlong Toei, Krung Thep Maha Nakhon 10110, Thailand", "phones": ["0853301020"], "location": {"lat": {"$numberDouble": "13.7224196"}, "lng": {"$numberDouble": "100.5804007"}}, "memo": "", "memo_th": "", "memoType": "TEXT", "picking_items": [], "delivery_items": [], "collect_payment": false, "items_price": {"$numberDouble": "0.0"}, "items_price_before_discount": {"$numberDouble": "0.0"}, "price_summary": {"delivery_fee": {"additional_fee": {}, "payment_method": "", "price_scheme_ref_id": "", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [], "coupon": {"code": "", "discount": {"$numberDouble": "0.0"}, "max_deduction": {"$numberDouble": "0.0"}}, "additional_service_fee": {"additional_service_items": [], "total": {"$numberDouble": "0.0"}, "commission": {"$numberDouble": "0.0"}}, "raw_base_fee": {"$numberDouble": "0.0"}, "base_fee": {"$numberDouble": "0.0"}, "road_fee": {"$numberDouble": "0.0"}, "shift_price_value": {"$numberDouble": "0.0"}, "mo_saving": {"$numberDouble": "0.0"}, "custom_ontop": {"$numberDouble": "0.0"}, "commission_rate": {"$numberDouble": "0.0"}, "starting_fee": {"$numberDouble": "0.0"}, "commission": {"$numberDouble": "0.0"}, "withholding_tax": {"$numberDouble": "0.0"}, "on_top_fare": {"$numberDouble": "0.0"}, "raw_on_top_fare": {"$numberDouble": "0.0"}, "raw_bundle_on_top_fare": {"$numberDouble": "0.0"}, "coin": {"$numberInt": "0"}, "raw_coin": {"$numberInt": "0"}, "raw_bundle_coin": {"$numberInt": "0"}, "on_top_commission_fare": {"$numberDouble": "0.0"}, "on_top_withholding_tax": {"$numberDouble": "0.0"}, "user_delivery_fee": {"$numberDouble": "0.0"}, "user_delivery_fee_before_discount": {"$numberDouble": "0.0"}, "distance_unit_fee": {"$numberDouble": "0.0"}, "sub_total": {"$numberDouble": "0.0"}, "total": {"$numberDouble": "0.0"}, "qr_prompt_pay_info": {"status": "", "is_user_resolve_qr": false}}, "experimental_delivery_fee": {"additional_fee": {}, "payment_method": "", "price_scheme_ref_id": "", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [], "coupon": {"code": "", "discount": {"$numberDouble": "0.0"}, "max_deduction": {"$numberDouble": "0.0"}}, "additional_service_fee": {"additional_service_items": [], "total": {"$numberDouble": "0.0"}, "commission": {"$numberDouble": "0.0"}}, "raw_base_fee": {"$numberDouble": "0.0"}, "base_fee": {"$numberDouble": "0.0"}, "road_fee": {"$numberDouble": "0.0"}, "shift_price_value": {"$numberDouble": "0.0"}, "mo_saving": {"$numberDouble": "0.0"}, "custom_ontop": {"$numberDouble": "0.0"}, "commission_rate": {"$numberDouble": "0.0"}, "starting_fee": {"$numberDouble": "0.0"}, "commission": {"$numberDouble": "0.0"}, "withholding_tax": {"$numberDouble": "0.0"}, "on_top_fare": {"$numberDouble": "0.0"}, "raw_on_top_fare": {"$numberDouble": "0.0"}, "raw_bundle_on_top_fare": {"$numberDouble": "0.0"}, "coin": {"$numberInt": "0"}, "raw_coin": {"$numberInt": "0"}, "raw_bundle_coin": {"$numberInt": "0"}, "on_top_commission_fare": {"$numberDouble": "0.0"}, "on_top_withholding_tax": {"$numberDouble": "0.0"}, "user_delivery_fee": {"$numberDouble": "0.0"}, "user_delivery_fee_before_discount": {"$numberDouble": "0.0"}, "distance_unit_fee": {"$numberDouble": "0.0"}, "sub_total": {"$numberDouble": "0.0"}, "total": {"$numberDouble": "0.0"}, "qr_prompt_pay_info": {"status": "", "is_user_resolve_qr": false}}, "item_fee": {"payment_method": "", "discounts": [], "quote_item_fee_before_discount": {"$numberDouble": "0.0"}, "item_fee": {"$numberDouble": "0.0"}, "sub_total": {"$numberDouble": "0.0"}, "total": {"$numberDouble": "0.0"}}, "total": {"$numberDouble": "0.0"}}, "estimated_delivery_time": {"$numberLong": "0"}, "distance": {"$numberDouble": "3.5"}, "experimental_distance": {"$numberDouble": "0.0"}, "source_snapping_distance": {"$numberDouble": "0.0"}, "experimental_source_snapping_distance": {"$numberDouble": "0.0"}, "target_snapping_distance": {"$numberDouble": "0.0"}, "experimental_target_snapping_distance": {"$numberDouble": "0.0"}, "experimental_estimated_delivery_time": {"$numberLong": "0"}, "delivery_status": "PICK_UP_DONE", "delivery_history": {"ARRIVED": {"$date": {"$numberLong": "1724051664927"}}, "INIT": {"$date": {"$numberLong": "1724051630183"}}, "ON_THE_WAY": {"$date": {"$numberLong": "1724051660605"}}, "PICK_UP_DONE": {"$date": {"$numberLong": "1724051714831"}}}, "info": {"service_type": "", "estimated_cooking_time": {"$numberLong": "0"}, "price_scheme": "", "restaurant_type": "", "restaurant_direction": {"restaurant_id": "", "parking": "", "direction": "", "memo": "", "updated_at": {"$date": {"$numberLong": "-62135596800000"}}}}, "pauses": {}, "metadata": {}, "do_not_disturb": false, "is_rain": false, "continue_reason": ""}, {"id": "", "name": "อู๋", "address": "1111 บ้านทุ่ง ทวีวัฒนา ทวีวัฒนา", "phones": [], "location": {"lat": {"$numberDouble": "13.784278"}, "lng": {"$numberDouble": "100.540724"}}, "memo": "mock PLEASE_MOCK_DRIVER LMD2XKYF6", "memo_th": "", "memoType": "TEXT", "picking_items": [], "delivery_items": [], "collect_payment": true, "items_price": {"$numberDouble": "0.0"}, "items_price_before_discount": {"$numberDouble": "0.0"}, "price_summary": {"delivery_fee": {"additional_fee": {}, "payment_method": "QR_PROMPTPAY", "price_scheme_ref_id": "84382f5835c041698dbc1348f4fd1fe4", "custom_ontops": [{"description": {"TH": "", "EN": ""}, "type": "surge_fee", "amount": {"$numberDouble": "29.0"}, "custom": false, "reference_ids": ["662793181efc98e1ae5580cd", "66279316c5b23bff3ec34590"]}], "discounts": [], "extra_charges": [], "on_top_scheme": [{"id": "3475158bc2054e9c8a07de4e493fa0b8", "scheme": "FLEXIBLE_FLAT_RATE", "name": "<PERSON>ncer ontop", "incentive_name": "", "type": "", "incentive_sources": [""], "reference_ids": [], "amount": {"$numberDouble": "14.0"}, "bundle_amount": {"$numberDouble": "14.0"}, "coin": {"$numberInt": "0"}, "bundle_coin": {"$numberInt": "0"}}], "coupon": {"code": "", "discount": {"$numberDouble": "0.0"}, "max_deduction": {"$numberDouble": "0.0"}}, "additional_service_fee": {"additional_service_items": [], "total": {"$numberDouble": "0.0"}, "commission": {"$numberDouble": "0.0"}}, "raw_base_fee": {"$numberDouble": "173.0"}, "base_fee": {"$numberDouble": "173.0"}, "road_fee": {"$numberDouble": "0.0"}, "shift_price_value": {"$numberDouble": "0.0"}, "mo_saving": {"$numberDouble": "0.0"}, "custom_ontop": {"$numberDouble": "29.0"}, "commission_rate": {"$numberDouble": "0.1"}, "starting_fee": {"$numberDouble": "56.0"}, "commission": {"$numberDouble": "20.2"}, "withholding_tax": {"$numberDouble": "0.0"}, "on_top_fare": {"$numberDouble": "14.0"}, "raw_on_top_fare": {"$numberDouble": "14.0"}, "raw_bundle_on_top_fare": {"$numberDouble": "14.0"}, "coin": {"$numberInt": "0"}, "raw_coin": {"$numberInt": "0"}, "raw_bundle_coin": {"$numberInt": "0"}, "on_top_commission_fare": {"$numberDouble": "1.4"}, "on_top_withholding_tax": {"$numberDouble": "0.0"}, "user_delivery_fee": {"$numberDouble": "0.0"}, "user_delivery_fee_before_discount": {"$numberDouble": "0.0"}, "distance_unit_fee": {"$numberDouble": "117.0"}, "sub_total": {"$numberDouble": "202.0"}, "total": {"$numberDouble": "202.0"}, "qr_prompt_pay_info": {"status": "WAITING_FOR_PAYMENT", "is_user_resolve_qr": false}}, "experimental_delivery_fee": {"additional_fee": {}, "payment_method": "QR_PROMPTPAY", "price_scheme_ref_id": "84382f5835c041698dbc1348f4fd1fe4", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [{"id": "3475158bc2054e9c8a07de4e493fa0b8", "scheme": "FLEXIBLE_FLAT_RATE", "name": "<PERSON>ncer ontop", "incentive_name": "", "type": "", "incentive_sources": [""], "reference_ids": [], "amount": {"$numberDouble": "14.0"}, "bundle_amount": {"$numberDouble": "14.0"}, "coin": {"$numberInt": "0"}, "bundle_coin": {"$numberInt": "0"}}], "coupon": {"code": "", "discount": {"$numberDouble": "0.0"}, "max_deduction": {"$numberDouble": "0.0"}}, "additional_service_fee": {"additional_service_items": [], "total": {"$numberDouble": "0.0"}, "commission": {"$numberDouble": "0.0"}}, "raw_base_fee": {"$numberDouble": "0.0"}, "base_fee": {"$numberDouble": "0.0"}, "road_fee": {"$numberDouble": "0.0"}, "shift_price_value": {"$numberDouble": "0.0"}, "mo_saving": {"$numberDouble": "0.0"}, "custom_ontop": {"$numberDouble": "0.0"}, "commission_rate": {"$numberDouble": "0.1"}, "starting_fee": {"$numberDouble": "0.0"}, "commission": {"$numberDouble": "0.0"}, "withholding_tax": {"$numberDouble": "0.0"}, "on_top_fare": {"$numberDouble": "14.0"}, "raw_on_top_fare": {"$numberDouble": "14.0"}, "raw_bundle_on_top_fare": {"$numberDouble": "14.0"}, "coin": {"$numberInt": "0"}, "raw_coin": {"$numberInt": "0"}, "raw_bundle_coin": {"$numberInt": "0"}, "on_top_commission_fare": {"$numberDouble": "1.4"}, "on_top_withholding_tax": {"$numberDouble": "0.0"}, "user_delivery_fee": {"$numberDouble": "0.0"}, "user_delivery_fee_before_discount": {"$numberDouble": "0.0"}, "distance_unit_fee": {"$numberDouble": "0.0"}, "sub_total": {"$numberDouble": "0.0"}, "total": {"$numberDouble": "0.0"}, "qr_prompt_pay_info": {"status": "", "is_user_resolve_qr": false}}, "item_fee": {"item_fee_before_discount": {"$numberDouble": "0.0"}, "payment_method": "QR_PROMPTPAY", "discounts": [], "quote_item_fee_before_discount": {"$numberDouble": "0.0"}, "item_fee": {"$numberDouble": "0.0"}, "sub_total": {"$numberDouble": "0.0"}, "total": {"$numberDouble": "0.0"}}, "total": {"$numberDouble": "202.0"}}, "estimated_delivery_time": {"$numberLong": "700"}, "distance": {"$numberDouble": "10398.4"}, "experimental_distance": {"$numberDouble": "0.0"}, "source_snapping_distance": {"$numberDouble": "25.543354"}, "experimental_source_snapping_distance": {"$numberDouble": "0.0"}, "target_snapping_distance": {"$numberDouble": "11.142482"}, "experimental_target_snapping_distance": {"$numberDouble": "0.0"}, "experimental_estimated_delivery_time": {"$numberLong": "0"}, "delivery_status": "DROP_OFF_DONE", "delivery_history": {"INIT": {"$date": {"$numberLong": "1724051630183"}}, "ON_THE_WAY": {"$date": {"$numberLong": "1724051714831"}}, "ARRIVED": {"$date": {"$numberLong": "1724051725443"}}, "DROP_OFF_DONE": {"$date": {"$numberLong": "1724051741344"}}}, "info": null, "pauses": {"WAITING_QR_PAYMENT": true}, "metadata": {}, "do_not_disturb": false, "is_rain": false, "continue_reason": ""}], "distribution_regions": ["BKK"], "distance": {"$numberDouble": "10398.0"}, "experimental_distance": {"$numberDouble": "0.0"}, "created_at": {"$date": {"$numberLong": "1724051646161"}}, "updated_at": {"$date": {"$numberLong": "1724051741349"}}, "options": {}, "note_to_driver": "mock p1d1 LMD750JHN", "pay_at_stop": {"$numberInt": "1"}, "special_event": [], "revenueprincipalmodel": false, "map_provider": "OSRM", "store_accept_half_half": false, "restaurant_chain_id": "", "ignore_driver_too_far_validation": false, "user_region": "BKK", "is_experimental_success": false, "order_id": "LMT-240819-170192788", "status": "COMPLETED", "head_to": {"$numberInt": "1"}, "revamped_status": true, "driver": "LMD750JHN", "region": "BKK", "history": {"ARRIVED_AT_1": {"$date": {"$numberLong": "1724051725443"}}, "DRIVE_TO_0": {"$date": {"$numberLong": "1724051660605"}}, "DRIVER_MATCHED": {"$date": {"$numberLong": "1724051660605"}}, "ARRIVED_AT_0": {"$date": {"$numberLong": "1724051664927"}}, "DRIVE_TO_1": {"$date": {"$numberLong": "1724051714831"}}, "READY": {"$date": {"$numberLong": "1724051660605"}}, "DROP_OFF_DONE": {"$date": {"$numberLong": "1724051741344"}}, "COMPLETED": {"$date": {"$numberLong": "1724051741349"}}, "ASSIGNING_DRIVER": {"$date": {"$numberLong": "1724051646161"}}}, "expire_at": {"$date": {"$numberLong": "1724052246161"}}, "rating_score": {"$numberInt": "0"}, "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": "", "label": "", "name": "", "ban_duration_in_minute": {"$numberInt": "0"}, "cancelled_with_quota": false, "cancellationratefree": false, "force_cancellation_rate_free": false, "should_auto_claim": false, "is_reassign": false, "reassign_only": false, "cancellation_source": "", "message": {"wma": {"en": "", "th": ""}}}, "cancel_detail_log": [], "is_fraud": false, "distribution": "AUTO_ASSIGN", "remarks": [], "fraud_status": "", "verified_rider_photo_urls": [], "should_verify": false, "delivering_photo_status": "", "delivering_photo_urls": [], "is_require_delivering_photo": false, "shifts": [], "prediction": {"estimated_cooking_time_second": {"$numberInt": "0"}, "estimated_user_waiting_time_second": {"$numberInt": "0"}}, "is_auto_assigned": true, "is_auto_accept": false, "is_fully_auto_accept": false, "assigned_to_queue": false, "is_b2b": false, "is_mo_secondary": false, "is_distributed": true, "defer_duration": {"$numberLong": "0"}, "is_throttled": false, "is_deferred": false, "throttled_round": {"$numberInt": "0"}, "accepted_with_batch_optimized": false, "delivering_round": {"$numberInt": "0"}, "trip_id": "TRIP-240819-676413427", "verified_device_id": "", "accepted_device_id": "1710484988280-6248012918422689049", "delivering_device_id": "", "predicted_completed_time": {"$date": {"$numberLong": "-62135596800000"}}, "history_location": {"COMPLETED": {"lat": {"$numberDouble": "13.7225217"}, "lng": {"$numberDouble": "100.5804233"}, "updated_at": {"$date": {"$numberLong": "1724051741344"}}}, "DRIVE_TO_0": {"lat": {"$numberDouble": "13.7225217"}, "lng": {"$numberDouble": "100.5804233"}, "updated_at": {"$date": {"$numberLong": "1724051660598"}}}, "ARRIVED_AT_0": {"lat": {"$numberDouble": "13.7225217"}, "lng": {"$numberDouble": "100.5804233"}, "updated_at": {"$date": {"$numberLong": "1724051664927"}}}, "DRIVE_TO_1": {"lat": {"$numberDouble": "13.7225217"}, "lng": {"$numberDouble": "100.5804233"}, "updated_at": {"$date": {"$numberLong": "1724051714831"}}}, "DRIVER_MATCHED": {"lat": {"$numberDouble": "13.7225217"}, "lng": {"$numberDouble": "100.5804233"}, "updated_at": {"$date": {"$numberLong": "1724051660605"}}}, "ARRIVED_AT_1": {"lat": {"$numberDouble": "13.7225217"}, "lng": {"$numberDouble": "100.5804233"}, "updated_at": {"$date": {"$numberLong": "1724051725443"}}}}, "has_exempted_words": false, "exempted_words": [], "driver_action_history": {}, "first_routed_uwt": {"$numberLong": "1"}, "leave_prev_stop_at": {"$date": {"$numberLong": "1724051660661"}}, "processed_by_single_distribution": true, "is_skip_incomplete_qr_promptpay_payment": false, "driver_type": "TESTER", "driver_vendor_id": "", "owner_changed_at": {"$date": {"$numberLong": "1724051630429"}}, "phone_contact": {"driver_id": "", "time": {"$date": {"$numberLong": "-62135596800000"}}}}]