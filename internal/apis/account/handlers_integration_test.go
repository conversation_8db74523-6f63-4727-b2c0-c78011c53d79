//go:build integration_test
// +build integration_test

package account_test

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	formServicePb "git.wndv.co/go/proto/lineman/form_service/v1"
	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/account"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	driverv1pb "git.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil/testdata"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestAccountAPI_Handlers(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)

	newUpdateDriverStatusHandlerRequest := func(r *account.UpdateDriverStatusRequest) *testutil.GinContextWithRecorder {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/account/status")
		gctx.Authorized(container.RedisTokenStore, "DEACTIVATED-001")
		gctx.Body().JSON(r).Build()
		return gctx
	}

	t.Run("should error when deactivated driver try to set new status", func(tt *testing.T) {
		driverStatusReq := &account.UpdateDriverStatusRequest{
			Status: model.StatusOnline,
		}

		gctx := newUpdateDriverStatusHandlerRequest(driverStatusReq)
		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(tt, http.StatusBadRequest)
	})
}

func TestAccountAPI_UpdateStatus_RaceCondition(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)

	newUpdateDriverStatusHandlerRequest := func(r *account.UpdateDriverStatusRequest) *testutil.GinContextWithRecorder {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/account/status")
		gctx.Authorized(container.RedisTokenStore, "TESTER_DRV_PATTAYA_ONLINE")
		gctx.Body().JSON(r).Build()
		return gctx
	}

	driverStatusReq := &account.UpdateDriverStatusRequest{
		Status: model.StatusOffline,
	}

	ctrl := gomock.NewController(t)
	d := mock_repository.NewMockDriverRepository(ctrl)

	realRepo := container.DriverRepository.Delegate
	container.AccountAPI.DriverRepository = repository.NewLatencyProxyDriverRepository(d, &metric.PrometheusMeter{})

	d.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, driverID string, opts ...repository.Option) (*model.Driver, error) {
		return realRepo.GetProfile(ctx, driverID, opts...)
	})
	d.EXPECT().SetProfile(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, profile *model.Driver, additionalSel bson.M) error {
		// simulate before update some other process update the status
		err := container.DriversDataStore.Update(ctx, bson.M{"driver_id": profile.DriverID}, bson.M{"$set": bson.M{"status": model.StatusAssigned}})
		require.NoError(t, err, "unable to driver status")

		return realRepo.SetProfile(ctx, profile, additionalSel)
	})

	gctx := newUpdateDriverStatusHandlerRequest(driverStatusReq)
	container.GinEngineRouter.HandleContext(gctx.GinCtx())
	gctx.AssertResponseCode(t, http.StatusBadRequest)

	var actual api.Error
	testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)
	require.Equal(t, "DRIVER_STATUS_CONFLICT", actual.Code)
	require.Equal(t, `unable to update driver status from ONLINE to OFFLINE`, actual.Message)
}

func TestAccountAPI_OfflineLater(t *testing.T) {
	t.Parallel()
	ctn := ittest.NewContainer(t)
	driverID := "ASSIGNED"

	t.Run("turning offline-later on works", func(t *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/account/offline-later/on")
		gctx.Authorized(ctn.RedisTokenStore, driverID)

		gctx.Send(ctn.GinEngineRouter)
		gctx.AssertResponseCode(t, http.StatusNoContent)
		profile, err := ctn.DriverRepository.GetProfile(context.Background(), driverID)
		require.NoError(t, err)
		assert.Equal(t, true, profile.OfflineLater)
	})
	t.Run("should error when deactivated driver try to set new status", func(t *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/account/offline-later/off")
		gctx.Authorized(ctn.RedisTokenStore, driverID)

		gctx.Send(ctn.GinEngineRouter)
		gctx.AssertResponseCode(t, http.StatusNoContent)
		profile, err := ctn.DriverRepository.GetProfile(context.Background(), driverID)
		require.NoError(t, err)
		assert.Equal(t, false, profile.OfflineLater)
	})
}

func TestAccountAPI_GetTier(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)

	newGetTierReqWithInvalidDriverID := func() *testutil.GinContextWithRecorder {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/tier")
		gctx.Authorized(container.RedisTokenStore, "FAKE_ID_NOT_EXIST")

		return gctx
	}

	newGetTierReq := func() *testutil.GinContextWithRecorder {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/tier")
		gctx.Authorized(container.RedisTokenStore, "DRV_PATTAYA_ONLINE")

		return gctx
	}

	newGetTierOfCustomBenefitRegionDriverReq := func() *testutil.GinContextWithRecorder {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/tier")
		gctx.Authorized(container.RedisTokenStore, "DEACTIVATED-001")

		return gctx
	}

	t.Run("return 200 with a default driver tier's benefit of the region", func(tt *testing.T) {
		gctx := newGetTierReq()
		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		var actual account.DriverTierResp
		testutil.DecodeJSON(tt, gctx.ResponseRecorder.Body, &actual)

		dbhelper := testutil.NewDBHelper(tt, container.DBConnectionForTest, "service_areas")
		var saDB model.ServiceArea
		dbhelper.Get(testdata.ObjectId(5), &saDB)

		gctx.AssertResponseCode(tt, http.StatusOK)
		assert.Equal(tt, model.DriverTierMember, actual.Tier)
		assert.Equal(tt, saDB.GetBenefit(), actual.Benefit)
	})

	t.Run("return 200 with a custom driver tier's benefit if there is it.", func(tt *testing.T) {
		gctx := newGetTierOfCustomBenefitRegionDriverReq()
		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		var actual account.DriverTierResp
		testutil.DecodeJSON(tt, gctx.ResponseRecorder.Body, &actual)

		gctx.AssertResponseCode(tt, http.StatusOK)
		assert.Equal(tt, model.DriverTierMember, actual.Tier)
		assert.Equal(tt, "<h1>Test</h1>", actual.Benefit)
	})

	t.Run("return 404 if driver id is not found.", func(tt *testing.T) {
		gctx := newGetTierReqWithInvalidDriverID()
		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		var actual account.DriverTierResp
		testutil.DecodeJSON(tt, gctx.ResponseRecorder.Body, &actual)

		gctx.AssertResponseCode(tt, http.StatusNotFound)
	})
}

func TestAccountAPI_GetTripHistory(t *testing.T) {
	driverId := "TRIP_HISTORY_DRV"

	t.Run("get trip history successfully", func(tt *testing.T) {
		os.Setenv("ENABLED_REVISION_DATASTORE", "true")
		defer os.Unsetenv("ENABLED_REVISION_DATASTORE")

		ctn := ittest.NewContainer(tt)
		gctx := testutil.NewContextWithRecorder()
		if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_trips"); err != nil {
			tt.Errorf("Unexpected error initfixture: %v", err)
		}

		gctx.SetGET("/v1/account/trip-history")
		gctx.SetQuery(fmt.Sprintf("serviceType=food&size=%v", 3))
		gctx.Authorized(ctn.RedisTokenStore, driverId)
		gctx.Send(ctn.GinEngineRouter)
		gctx.AssertResponseCode(tt, http.StatusOK)

		var resp account.GetTripHistoryResponse
		gctx.DecodeJSONResponse(&resp)
		require.Len(tt, resp.Data, 2)

		// Completed trip
		require.Equal(tt, "TRIP_HISTORY_COMPLETED", resp.Data[0].TripID)
		require.Len(tt, resp.Data[0].Routes, 2)
		require.Len(tt, resp.Data[0].InactiveOrders, 0)
		require.Equal(tt, model.TripStatusCompleted, resp.Data[0].Status)

		// Canceled trip
		require.Equal(tt, "TRIP_HISTORY_CANCELED", resp.Data[1].TripID)
		require.Len(tt, resp.Data[1].Routes, 0)
		require.Len(tt, resp.Data[1].InactiveOrders, 1)
		require.Equal(tt, model.TripStatusCanceled, resp.Data[1].Status)
	})
}

func TestAccountAPI_UpdateDeviceToken(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)

	t.Run("should update value in mongo", func(tt *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/account/device-token")
		gctx.SetBody(testutil.JSON(&account.UpdateDeviceTokenRequest{
			DeviceToken: "test_device_token",
			DeviceID:    "test_device_id",
			AdvertiseID: "test_advertise_id",
		}))
		gctx.Authorized(container.RedisTokenStore, "DRV_PATTAYA_ONLINE")

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 200)
		driver, err := container.DriverRepository.GetProfile(context.Background(), "DRV_PATTAYA_ONLINE")
		require.NoError(tt, err)

		require.Equal(tt, "test_device_token", driver.DeviceToken)
		require.Equal(tt, "test_device_id", driver.DeviceID)
		require.Equal(tt, "test_advertise_id", driver.AdvertiseID)
	})
}

func TestAccountAPI_UpdateSocketID(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)

	t.Run("should update value in mongo", func(tt *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/account/socket-id")
		gctx.SetBody(testutil.JSON(&account.UpdateSocketIDRequest{
			SocketID: "socket_id",
		}))
		gctx.Authorized(container.RedisTokenStore, "DRV_PATTAYA_ONLINE")

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 204)
		driver, err := container.DriverRepository.GetProfile(context.Background(), "DRV_PATTAYA_ONLINE")
		require.NoError(tt, err)
		require.Equal(tt, "socket_id", driver.SocketID)
	})
}

func TestAccountAPI_ProfileIntegration(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)

	t.Run("should return 200 when PDPA is enabled", func(tt *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/profile")
		gctx.Authorized(container.RedisTokenStore, "DRV_PATTAYA_ONLINE")
		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		var resp account.DriverProfileResponse
		gctx.DecodeJSONResponse(&resp)

		require.NotEqual(tt, 0, len(resp.Pdpa))
	})

	t.Run("should return 200 when PDPA is disable", func(tt *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/profile")
		gctx.Authorized(container.RedisTokenStore, "DRV_PATTAYA_ONLINE")
		// disable pdpa
		container.AccountAPI.PDPACfg.Enabled = false

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		var resp account.DriverProfileResponse
		gctx.DecodeJSONResponse(&resp)

		require.Equal(tt, 0, len(resp.Pdpa))
	})

	t.Run("bcp in-sync due to no action in bcp", func(tt *testing.T) {
		container.SimpleUnleash.SetEnabled(featureflag.IsBCPFlagEnabled.Name, true)

		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/profile")
		gctx.Authorized(container.RedisTokenStore, "DRV_PATTAYA_ONLINE")

		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		var resp account.DriverProfileResponse
		gctx.DecodeJSONResponse(&resp)

		require.Equal(tt, false, resp.BCPStateOutSync)
	})
}

func TestAccountAPI_Wallet(t *testing.T) {
	t.Parallel()
	container := ittest.NewContainer(t)
	err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_withdraw")
	if err != nil {
		panic(err)
	}

	withdrawWallet := func(driverID string, amount int) *testutil.GinContextWithRecorder {
		req := struct {
			Type   string
			Amount int
		}{
			"withdraw",
			amount,
		}

		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/account/wallet")
		gctx.Authorized(container.RedisTokenStore, driverID)
		gctx.Body().JSON(req).Build()

		return gctx
	}

	t.Run("should withdraw success", func(tt *testing.T) {
		gctx := withdrawWallet("DRV_PATTAYA_ONLINE", 200)
		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		var resp account.DriverTransactionResponse
		gctx.DecodeJSONResponse(&resp)

		gctx.AssertResponseCode(tt, http.StatusOK)
		assert.Equal(tt, 200.00, resp.Transactions[0].Amount)
		assert.Equal(tt, 9800.00, resp.WalletBalance)
	})

	t.Run("should get error, if amount less than config minimum amount", func(tt *testing.T) {
		gctx := withdrawWallet("DRV_PATTAYA_ONLINE", 100)
		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(tt, http.StatusBadRequest)
	})
	t.Run("should success, if withdraw amount is less than config minimum amount but driver is banned.", func(tt *testing.T) {
		gctx := withdrawWallet("BANNED_DRIVER_2", 100)
		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		var resp account.DriverTransactionResponse
		gctx.DecodeJSONResponse(&resp)

		gctx.AssertResponseCode(tt, http.StatusOK)
		assert.Equal(tt, "BANNED_DRIVER_2", resp.DriverID)
		assert.Equal(tt, 100.00, resp.Transactions[0].Amount)
		assert.Equal(tt, "PENDING", resp.Transactions[0].Status)
		assert.Equal(tt, 9900.00, resp.WalletBalance)
	})

	t.Run("should success, if profile_status is UPDATE_PENDING and profile is expired", func(tt *testing.T) {
		gctx := withdrawWallet("UPDATE_PENDING", 200)
		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		var resp account.DriverTransactionResponse
		gctx.DecodeJSONResponse(&resp)

		gctx.AssertResponseCode(tt, http.StatusOK)
		assert.Equal(tt, 200.00, resp.Transactions[0].Amount)
		assert.Equal(tt, 9800.00, resp.WalletBalance)
	})

	t.Run("should success, if profile_status is REQUESTED_REUPDATE and profile is expired", func(tt *testing.T) {
		gctx := withdrawWallet("REQUESTED_REUPDATE", 200)
		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		var resp account.DriverTransactionResponse
		gctx.DecodeJSONResponse(&resp)

		gctx.AssertResponseCode(tt, http.StatusOK)
		assert.Equal(tt, 200.00, resp.Transactions[0].Amount)
		assert.Equal(tt, 9800.00, resp.WalletBalance)
	})

	t.Run("should fail, if profile_status is PENDING and profile is expired", func(tt *testing.T) {
		gctx := withdrawWallet("PENDING", 200)
		container.GinEngineRouter.HandleContext(gctx.GinCtx())

		var resp account.DriverTransactionResponse
		gctx.DecodeJSONResponse(&resp)

		gctx.AssertResponseCode(tt, http.StatusBadRequest)
	})
}

func TestAccountAPI_GetMyCoinHistoryIntegration(t *testing.T) {
	t.Parallel()
	ctn := ittest.NewContainer(t)
	if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_my_coin_history"); err != nil {
		t.Errorf("Unexpected error initfixture: %v", err)
	}

	t.Run("should success then when convert coin cash txn not found, have yesterday reward txn also exclude type convert coin cash", func(tt *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/my-coin/history")
		gctx.Authorized(ctn.RedisTokenStore, "DRV_PATTAYA_ONLINE")

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 200)
		var actual account.CoinHistoryResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)
		assert.Equal(t, "อินเซนทีฟจะถูกโอนไปยังวอลเล็ตภายใน 20:00 น. หลังเสร็จภารกิจหนึ่งวัน", actual.Description)
		assert.Equal(t, 50.0, actual.Progress.Current)
	})

	t.Run("should success then when convert coin cash txn not found, have yesterday reward txn also exclude type not enough coin", func(tt *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/my-coin/history")
		gctx.Authorized(ctn.RedisTokenStore, "DRIV_NOT_ENOUGH_COIN")

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 200)
		var actual account.CoinHistoryResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)
		assert.Equal(t, 10.0, actual.Progress.Current)
	})

	t.Run("should success then when convert coin cash txn found", func(tt *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/my-coin/history")
		gctx.Authorized(ctn.RedisTokenStore, "TEST_COIN_HISTORY")

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 200)
		var actual account.CoinHistoryResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)
		assert.Equal(t, "อินเซนทีฟจะถูกโอนไปยังวอลเล็ตภายใน 20:00 น. หลังเสร็จภารกิจหนึ่งวัน", actual.Description)
		assert.Equal(t, float64(40), actual.Progress.UpcomingReward)
		assert.Equal(t, float64(25), actual.Progress.Current)
		assert.Equal(t, float64(30), actual.Progress.Goal)
	})

	t.Run("should success when no yesterday coin", func(tt *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/my-coin/history")
		gctx.Authorized(ctn.RedisTokenStore, "NO_YESTERDAY_COIN")

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		var response api.Error
		gctx.DecodeJSONResponse(&response)
		gctx.AssertResponseCode(t, http.StatusNotFound)
		assert.Equal(t, "driver have no yesterday coin", response.Message)
		assert.Equal(t, "NO_YESTERDAY_COIN", response.Code)
	})
}

func TestAccountAPI_RequestUpdateProfile(t *testing.T) {
	t.Run("Should request update profile success", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPUT("/v1/account/request-update-profile")
		ctx.Authorized(ctn.RedisTokenStore, "DRV_PATTAYA_ONLINE")
		ctx.Body().MultipartForm().
			String("licenseId", "LCID").
			String("expirationDate", "2022-05-31T15:04:05.000Z").
			File("driverImage", "avatar.jpg", nil).
			File("driverLicenseImage", "license.jpg", nil).
			File("vehicleImage", "vehicle.jpg", nil).
			File("vehicleRegistrationImage", "vehicle_registration.jpg", nil).
			String("vehicleRegistrationDate", "2021-05-31T15:04:05.000Z").
			String("vehiclePlateNumber", "7777").
			Build()

		ctn.GinEngineRouter.HandleContext(ctx.GinCtx())

		var response account.ProfileRes
		ctx.AssertResponseCode(t, 200)
		ctx.DecodeJSONResponse(&response)
		require.Equal(t, model.ProfileStatusUpdatePending, response.RequestUpdateProfile[0].Status)
	})

	t.Run("Should not allow banned driver to request update profile", func(tt *testing.T) {
		tt.Parallel()
		ctn := ittest.NewContainer(tt)
		if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_account"); err != nil {
			panic(err)
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPUT("/v1/account/request-update-profile")
		ctx.Authorized(ctn.RedisTokenStore, "DRV_BANNED")
		ctx.Body().MultipartForm().
			String("licenseId", "LCID").
			String("expirationDate", "2022-05-31T15:04:05.000Z").
			File("driverImage", "avatar.jpg", nil).
			File("driverLicenseImage", "license.jpg", nil).
			File("vehicleImage", "vehicle.jpg", nil).
			Build()

		ctn.GinEngineRouter.HandleContext(ctx.GinCtx())

		var response api.Error
		ctx.AssertResponseCode(tt, 400)
		ctx.DecodeJSONResponse(&response)
		logrus.Info(response)
		require.Equal(tt, "DRIVER_BANNED", response.Code)
		require.Equal(tt, "driver got banned", response.Message)
		require.Equal(tt, "TEST_MESSAGE", response.Info["detail"])
		require.Equal(tt, "DRV_BANNED", response.Info["driverId"])
	})
}

func TestAccountAPI_GetPerformance(t *testing.T) {
	t.Run("Empty state new driver doesn't has driver_order_info document", func(t *testing.T) {
		getPerformance := func(tokenStore auth.TokenStorage, types string, driverID string, granularity string, date string) *testutil.GinContextWithRecorder {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetGET("/v1/account/performance")
			gctx.SetQuery(fmt.Sprintf("types=%v&granularity=%v&date=%v", types, granularity, url.QueryEscape(date)))
			gctx.Authorized(tokenStore, driverID)

			return gctx
		}

		// Given
		container := ittest.NewContainer(t)
		sim := newAttrDataSimulator(container, t)
		sim.SetSystemTime("2022-04-25T13:00:00+07:00")
		defer sim.UnfreezeTime()

		driverID := "driver-A"
		sim.CreateDriver(driverID)

		t.Run("empty type", func(t *testing.T) {
			// When
			gctx := getPerformance(container.RedisTokenStore, "", "driver-A", "daily", "2022-04-25T13:00:00+07:00")
			container.GinEngineRouter.HandleContext(gctx.GinCtx())

			// Then
			assert.JSONEq(t, `{"ar":null,"cr":null,"attr":null,"updated_at":null}`, gctx.ResponseRecorder.Body.String())

			var resp account.DriverPerformanceResponse
			gctx.DecodeJSONResponse(&resp)

			gctx.AssertResponseCode(t, http.StatusOK)
			assert.Nil(t, resp.AR)
			assert.Nil(t, resp.CR)
			assert.Nil(t, resp.ATTR)
			assert.Nil(t, resp.UpdatedAt)
		})

		t.Run("ar type", func(t *testing.T) {
			// When
			gctx := getPerformance(container.RedisTokenStore, "ar", "driver-A", "daily", "2022-04-25T13:00:00+07:00")
			container.GinEngineRouter.HandleContext(gctx.GinCtx())

			// Then
			assert.JSONEq(t, `{"ar":null,"cr":null,"attr":null,"updated_at":null}`, gctx.ResponseRecorder.Body.String())

			var resp account.DriverPerformanceResponse
			gctx.DecodeJSONResponse(&resp)

			gctx.AssertResponseCode(t, http.StatusOK)
			assert.Nil(t, resp.AR)
			assert.Nil(t, resp.CR)
			assert.Nil(t, resp.ATTR)
			assert.Nil(t, resp.UpdatedAt)
		})

		t.Run("cr type", func(t *testing.T) {
			// When
			gctx := getPerformance(container.RedisTokenStore, "cr", "driver-A", "daily", "2022-04-25T13:00:00+07:00")
			container.GinEngineRouter.HandleContext(gctx.GinCtx())

			// Then
			assert.JSONEq(t, `{"ar":null,"cr":null,"attr":null,"updated_at":null}`, gctx.ResponseRecorder.Body.String())

			var resp account.DriverPerformanceResponse
			gctx.DecodeJSONResponse(&resp)

			gctx.AssertResponseCode(t, http.StatusOK)
			assert.Nil(t, resp.AR)
			assert.Nil(t, resp.CR)
			assert.Nil(t, resp.ATTR)
			assert.Nil(t, resp.UpdatedAt)
		})

		t.Run("attr type", func(t *testing.T) {
			// When
			gctx := getPerformance(container.RedisTokenStore, "attr", "driver-A", "daily", "2022-04-25T13:00:00+07:00")
			container.GinEngineRouter.HandleContext(gctx.GinCtx())

			// Then
			assert.JSONEq(t, `{"ar":null,"cr":null,"attr":null,"updated_at":null}`, gctx.ResponseRecorder.Body.String())

			var resp account.DriverPerformanceResponse
			gctx.DecodeJSONResponse(&resp)

			gctx.AssertResponseCode(t, http.StatusOK)
			assert.Nil(t, resp.AR)
			assert.Nil(t, resp.CR)
			assert.Nil(t, resp.ATTR)
			assert.Nil(t, resp.UpdatedAt)
		})
	})

	t.Run("ATTR", func(t *testing.T) {
		getPerformance := func(tokenStore auth.TokenStorage, driverID string, granularity string, date string) *testutil.GinContextWithRecorder {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetGET("/v1/account/performance")
			gctx.SetQuery(fmt.Sprintf("types=attr&granularity=%v&date=%v", granularity, url.QueryEscape(date)))
			gctx.Authorized(tokenStore, driverID)

			return gctx
		}

		t.Run("Attend only 1 shift", func(t *testing.T) {
			t.Run("Attended w/o break time", func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-04-25T13:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)
				shift := sim.AddShift("2022-04-25T10:30:00+07:00", "2022-04-25T13:00:00+07:00", 20, true)
				sim.DriverBookShift(shift, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift, []string{"10.20-13.30"})

				// When
				gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-25T13:00:00+07:00")
				container.GinEngineRouter.HandleContext(gctx.GinCtx())

				// Then
				var resp account.DriverPerformanceResponse
				gctx.DecodeJSONResponse(&resp)

				gctx.AssertResponseCode(t, http.StatusOK)
				require.NotNil(t, resp.ATTR)
				assert.Equal(t, 150.0, resp.ATTR.TotalAttendingTime)
				assert.Equal(t, 150.0, resp.ATTR.TotalBookingTime)
				assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
				assert.Equal(t, 100, resp.ATTR.PercentageChange)
				assert.Equal(t, float64(100), resp.ATTR.ATTR)
				assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
			})

			t.Run("Break time within quota", func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-04-25T13:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)
				shift := sim.AddShift("2022-04-25T10:30:00+07:00", "2022-04-25T13:00:00+07:00", 20, true)
				sim.DriverBookShift(shift, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift, []string{"10.30-12.45"})

				// When
				gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-25T13:00:00+07:00")
				container.GinEngineRouter.HandleContext(gctx.GinCtx())

				// Then
				var resp account.DriverPerformanceResponse
				gctx.DecodeJSONResponse(&resp)

				gctx.AssertResponseCode(t, http.StatusOK)
				require.NotNil(t, resp.ATTR)
				assert.Equal(t, 150.0, resp.ATTR.TotalAttendingTime)
				assert.Equal(t, 150.0, resp.ATTR.TotalBookingTime)
				assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
				assert.Equal(t, 100, resp.ATTR.PercentageChange)
				assert.Equal(t, float64(100), resp.ATTR.ATTR)
				assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
			})

			t.Run("Break time exceed quota (shift: 150, quota: 20, break: 30)", func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-04-25T13:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)
				shift := sim.AddShift("2022-04-25T10:30:00+07:00", "2022-04-25T13:00:00+07:00", 20, true)
				sim.DriverBookShift(shift, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift, []string{"10.30-11.00", "11.04-12.16", "12.42-13.00"})

				// When
				gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-25T13:00:00+07:00")
				container.GinEngineRouter.HandleContext(gctx.GinCtx())

				// Then
				var resp account.DriverPerformanceResponse
				gctx.DecodeJSONResponse(&resp)

				gctx.AssertResponseCode(t, http.StatusOK)
				require.NotNil(t, resp.ATTR)
				assert.Equal(t, 140.0, resp.ATTR.TotalAttendingTime)
				assert.Equal(t, 150.0, resp.ATTR.TotalBookingTime)
				assert.Equal(t, 10.0, resp.ATTR.TotalExceedQuotaBreakTime)
				assert.Equal(t, 93, resp.ATTR.PercentageChange)
				assert.Equal(t, float64(93.33), resp.ATTR.ATTR)
				assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
			})

			t.Run("Break time exceed quota (shift: 150, quota: 20, break: 149)", func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-04-25T13:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)
				shift := sim.AddShift("2022-04-25T10:30:00+07:00", "2022-04-25T13:00:00+07:00", 20, true)
				sim.DriverBookShift(shift, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift, []string{"10.30-10.31"})

				// When
				gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-25T13:00:00+07:00")
				container.GinEngineRouter.HandleContext(gctx.GinCtx())

				// Then
				var resp account.DriverPerformanceResponse
				gctx.DecodeJSONResponse(&resp)

				gctx.AssertResponseCode(t, http.StatusOK)
				require.NotNil(t, resp.ATTR)
				assert.Equal(t, 21.0, resp.ATTR.TotalAttendingTime)
				assert.Equal(t, 150.0, resp.ATTR.TotalBookingTime)
				assert.Equal(t, 129.0, resp.ATTR.TotalExceedQuotaBreakTime)
				assert.Equal(t, 14, resp.ATTR.PercentageChange)
				assert.Equal(t, float64(14), resp.ATTR.ATTR)
				assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
			})

			t.Run("Break time exceed quota (shift: 150, quota: 20, break: 150)", func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-04-25T13:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)
				shift := sim.AddShift("2022-04-25T10:30:00+07:00", "2022-04-25T13:00:00+07:00", 20, true)
				sim.DriverBookShift(shift, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift, []string{"10.31-10.31"})

				// When
				gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-25T13:00:00+07:00")
				container.GinEngineRouter.HandleContext(gctx.GinCtx())

				// Then
				var resp account.DriverPerformanceResponse
				gctx.DecodeJSONResponse(&resp)

				gctx.AssertResponseCode(t, http.StatusOK)
				require.NotNil(t, resp.ATTR)
				assert.Equal(t, 20.0, resp.ATTR.TotalAttendingTime)
				assert.Equal(t, 150.0, resp.ATTR.TotalBookingTime)
				assert.Equal(t, 130.0, resp.ATTR.TotalExceedQuotaBreakTime)
				assert.Equal(t, 13, resp.ATTR.PercentageChange)
				assert.Equal(t, float64(13.33), resp.ATTR.ATTR)
				assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
			})

			t.Run("Break time all shift", func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-04-25T13:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)
				shift := sim.AddShift("2022-04-25T10:30:00+07:00", "2022-04-25T13:00:00+07:00", 20, true)
				sim.DriverBookShift(shift, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift, []string{"7.30-9.31", "13.01-15.00"})

				// When
				gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-25T13:00:00+07:00")
				container.GinEngineRouter.HandleContext(gctx.GinCtx())

				// Then
				var resp account.DriverPerformanceResponse
				gctx.DecodeJSONResponse(&resp)

				gctx.AssertResponseCode(t, http.StatusOK)
				require.NotNil(t, resp.ATTR)
				assert.Equal(t, 0.0, resp.ATTR.TotalAttendingTime)
				assert.Equal(t, 150.0, resp.ATTR.TotalBookingTime)
				assert.Equal(t, 150.0, resp.ATTR.TotalExceedQuotaBreakTime)
				assert.Equal(t, 0, resp.ATTR.PercentageChange)
				assert.Equal(t, float64(0), resp.ATTR.ATTR)
				assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
			})
		})

		t.Run("Attend > 1 shift", func(t *testing.T) {
			t.Run(`
			- Multiple period in 1 day
			- Exceed break time in some period
			- Break time within all quota
			`, func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-04-25T22:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)

				shift1 := sim.AddShift("2022-04-25T08:30:00+07:00", "2022-04-25T10:00:00+07:00", 10, true)
				sim.DriverBookShift(shift1, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift1, []string{"08.30-08.30", "08.32-09.57"})

				shift2 := sim.AddShift("2022-04-25T12:00:00+07:00", "2022-04-25T14:00:00+07:00", 20, true)
				sim.DriverBookShift(shift2, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift2, []string{"11.30-12.00", "12.20-15.00"})

				shift3 := sim.AddShift("2022-04-25T18:30:00+07:00", "2022-04-25T21:00:00+07:00", 20, true)
				sim.DriverBookShift(shift3, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift3, []string{"18.00-20.35"})

				// When
				t.Run("Daily view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-25T22:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 355.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 360.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 5.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 99, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(98.61), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})

				t.Run("Weekly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "weekly", "2022-04-25T22:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 355.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 360.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 5.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 99, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(98.61), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})

				t.Run("Monthly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "monthly", "2022-04-25T22:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 355.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 360.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 5.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 99, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(98.61), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})
			})

			t.Run(`
			- Multiple period in 1 week
			- No break time
			`, func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-04-26T11:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)

				shift1 := sim.AddShift("2022-04-25T08:30:00+07:00", "2022-04-25T10:00:00+07:00", 10, true)
				sim.DriverBookShift(shift1, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-25", shift1, 90)

				shift2 := sim.AddShift("2022-04-25T18:30:00+07:00", "2022-04-25T20:45:00+07:00", 20, true)
				sim.DriverBookShift(shift2, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-25", shift2, 135)

				shift3 := sim.AddShift("2022-04-26T08:30:00+07:00", "2022-04-26T10:00:00+07:00", 20, true)
				sim.DriverBookShift(shift3, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift3, []string{"08.30-10.00"})

				// When
				t.Run("Daily view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-26T11:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 90.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 90.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 0, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(100), resp.ATTR.ATTR)
					assert.Equal(t, float64(100), resp.ATTR.PreviousATTR)
				})

				t.Run("Weekly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "weekly", "2022-04-26T11:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 315.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 315.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 100, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(100), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})

				t.Run("Monthly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "monthly", "2022-04-26T11:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 315.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 315.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 100, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(100), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})
			})

			t.Run(`
			- Multiple period in different week
			- Exceed break time in some period
			- Break time within all quota
			`, func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-04-26T11:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)

				shift1 := sim.AddShift("2022-04-21T08:30:00+07:00", "2022-04-21T10:00:00+07:00", 10, true)
				sim.DriverBookShift(shift1, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-21", shift1, 90)

				shift2 := sim.AddShift("2022-04-23T18:30:00+07:00", "2022-04-23T20:45:00+07:00", 20, true)
				sim.DriverBookShift(shift2, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-23", shift2, 135)

				shift3 := sim.AddShift("2022-04-26T08:30:00+07:00", "2022-04-26T10:00:00+07:00", 10, true)
				sim.DriverBookShift(shift3, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift3, []string{"06.30-08.40", "09.20-10.00"})

				// When
				t.Run("Daily view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-26T11:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 60.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 90.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 30.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 67, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(66.67), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})

				t.Run("Weekly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "weekly", "2022-04-26T11:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 60.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 90.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 30.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, -33, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(66.67), resp.ATTR.ATTR)
					assert.Equal(t, float64(100), resp.ATTR.PreviousATTR)
				})

				t.Run("Monthly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "monthly", "2022-04-26T11:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 285.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 315.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 30.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 90, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(90.48), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})
			})

			t.Run(`
			- Multiple period in different week
			- Exceed break time each period and all quota
			`, func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-04-26T17:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)

				shift1 := sim.AddShift("2022-04-21T08:30:00+07:00", "2022-04-21T10:00:00+07:00", 10, true)
				sim.DriverBookShift(shift1, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-21", shift1, 40)

				shift2 := sim.AddShift("2022-04-23T18:30:00+07:00", "2022-04-23T20:45:00+07:00", 20, true)
				sim.DriverBookShift(shift2, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-23", shift2, 90)

				shift3 := sim.AddShift("2022-04-26T08:30:00+07:00", "2022-04-26T10:00:00+07:00", 10, true)
				sim.DriverBookShift(shift3, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift3, []string{"08.30-09.01", "09.05-09.05", "09.11-10.00"})

				shift4 := sim.AddShift("2022-04-26T14:30:00+07:00", "2022-04-26T16:00:00+07:00", 10, true)
				sim.DriverBookShift(shift4, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift4, []string{"14.30-15.15", "15.55-16.00", "16.00-16.05"})

				// When
				t.Run("Daily view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-26T17:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 150.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 180.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 30.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 83, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(83.33), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})

				t.Run("Weekly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "weekly", "2022-04-26T17:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 150.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 180.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 30.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 26, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(83.33), resp.ATTR.ATTR)
					assert.Equal(t, float64(57.78), resp.ATTR.PreviousATTR)
				})

				t.Run("Monthly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "monthly", "2022-04-26T17:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 280.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 405.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 125.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 69, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(69.14), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})
			})

			t.Run(`
			- Multiple period in different month
			- Exceed break time each period and all quota
			`, func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-05-03T17:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)

				shift1 := sim.AddShift("2022-04-21T08:30:00+07:00", "2022-04-21T10:00:00+07:00", 10, true)
				sim.DriverBookShift(shift1, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-21", shift1, 40)

				shift2 := sim.AddShift("2022-04-30T18:30:00+07:00", "2022-04-30T20:45:00+07:00", 20, true)
				sim.DriverBookShift(shift2, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-30", shift2, 90)

				shift3 := sim.AddShift("2022-05-03T08:30:00+07:00", "2022-05-03T10:00:00+07:00", 10, true)
				sim.DriverBookShift(shift3, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift3, []string{"08.30-09.01", "09.05-09.05", "09.11-10.00"})

				shift4 := sim.AddShift("2022-05-03T14:30:00+07:00", "2022-05-03T16:00:00+07:00", 10, true)
				sim.DriverBookShift(shift4, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift4, []string{"14.30-15.15", "15.55-16.00", "16.00-16.05"})

				// When
				t.Run("Daily view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-05-03T17:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 150.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 180.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 30.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 83, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(83.33), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})

				t.Run("Weekly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "weekly", "2022-05-03T17:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 150.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 180.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 30.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 17, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(83.33), resp.ATTR.ATTR)
					assert.Equal(t, float64(66.67), resp.ATTR.PreviousATTR)
				})

				t.Run("Monthly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "monthly", "2022-05-03T17:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 150.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 180.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 30.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 26, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(83.33), resp.ATTR.ATTR)
					assert.Equal(t, float64(57.78), resp.ATTR.PreviousATTR)
				})
			})

			t.Run(`
			- Multiple period different month
			- Not attend shift period
			`, func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-05-03T17:00:00+07:00")
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)

				shift1 := sim.AddShift("2022-04-21T08:30:00+07:00", "2022-04-21T10:00:00+07:00", 10, true)
				sim.DriverBookShift(shift1, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-21", shift1, 0)

				shift2 := sim.AddShift("2022-04-23T18:30:00+07:00", "2022-04-23T20:45:00+07:00", 20, true)
				sim.DriverBookShift(shift2, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-23", shift2, 0)

				shift3 := sim.AddShift("2022-05-03T08:30:00+07:00", "2022-05-03T10:00:00+07:00", 10, true)
				sim.DriverBookShift(shift3, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift3, []string{"00.00-00.30"})

				shift4 := sim.AddShift("2022-05-03T14:30:00+07:00", "2022-05-03T16:00:00+07:00", 10, true)
				sim.DriverBookShift(shift4, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, shift4, []string{"11.00-14.31", "14.36-15.00", "15.25-15.30", "15.35-15.40", "15.45-17.00"})

				// When
				t.Run("Daily view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-05-03T17:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 60.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 180.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 120.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 33, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(33.33), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})

				t.Run("Weekly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "weekly", "2022-05-03T17:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 60.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 180.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 120.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 33, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(33.33), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})

				t.Run("Monthly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "monthly", "2022-05-03T17:00:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 60.0, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 180.0, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 120.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 33, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(33.33), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})
			})
		})

		t.Run("Edge cases", func(t *testing.T) {
			t.Run("Driver got deleted attendance stats by migration script and still has shifts before cutoff 2022-04-21", func(t *testing.T) {
				t.Run("Has first shift on 2022-04-21", func(t *testing.T) {
					// Given
					container := ittest.NewContainer(t)
					sim := newAttrDataSimulator(container, t)
					sim.SetSystemTime("2022-04-21T21:00:00+07:00")
					defer sim.UnfreezeTime()

					driverID := "driver-A"
					driver := sim.CreateDriver(driverID)
					sim.DriverAcceptOrder(driverID)

					// Without attendance stats because we had run script to delete all attendance stat before 2022-04-21
					shift1 := sim.AddShift("2022-04-14T08:30:00+07:00", "2022-04-14T10:00:00+07:00", 10, true)
					sim.DriverBookShift(shift1, driver.DriverID)

					// Without attendance stats because we had run script to delete all attendance stat before 2022-04-21
					shift2 := sim.AddShift("2022-04-15T18:30:00+07:00", "2022-04-15T20:45:00+07:00", 20, true)
					sim.DriverBookShift(shift2, driver.DriverID)

					// Has shift on 21-APR
					shift3 := sim.AddShift("2022-04-21T18:30:00+07:00", "2022-04-21T20:45:00+07:00", 20, true)
					sim.DriverBookShift(shift3, driver.DriverID)
					sim.DriverAttendShift(driver.DriverID, shift3, []string{"18.30-20.45"})

					// When
					t.Run("Daily view on 2022-04-14", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-14T17:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 0.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 0, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(0), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Daily view on 2022-04-15", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-15T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 0.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 0, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(0), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Daily view on 2022-04-21", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-21T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 135.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 135.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 100, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(100), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Weekly view", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "weekly", "2022-04-21T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 135.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 135.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 100, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(100), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Monthly view", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "monthly", "2022-04-21T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 135.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 135.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 100, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(100), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})
				})

				t.Run("Has first shift on 2022-04-29", func(t *testing.T) {
					// Given
					container := ittest.NewContainer(t)
					sim := newAttrDataSimulator(container, t)
					sim.SetSystemTime("2022-04-29T21:00:00+07:00")
					defer sim.UnfreezeTime()

					driverID := "driver-A"
					driver := sim.CreateDriver(driverID)
					sim.DriverAcceptOrder(driverID)

					// Without attendance stats because we had run script to delete all attendance stat before 2022-04-21
					shift1 := sim.AddShift("2022-04-14T08:30:00+07:00", "2022-04-14T10:00:00+07:00", 10, true)
					sim.DriverBookShift(shift1, driver.DriverID)

					// Without attendance stats because we had run script to delete all attendance stat before 2022-04-21
					shift2 := sim.AddShift("2022-04-15T18:30:00+07:00", "2022-04-15T20:45:00+07:00", 20, true)
					sim.DriverBookShift(shift2, driver.DriverID)

					// Has shift on 29-APR
					shift3 := sim.AddShift("2022-04-29T18:30:00+07:00", "2022-04-29T20:45:00+07:00", 20, true)
					sim.DriverBookShift(shift3, driver.DriverID)
					sim.DriverAttendShift(driver.DriverID, shift3, []string{"18.30-20.45"})

					// When
					t.Run("Daily view on 2022-04-14", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-14T17:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 0.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 0, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(0), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Daily view on 2022-04-15", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-15T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 0.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 0, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(0), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Daily view on 2022-04-29", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-29T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 135.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 135.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 100, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(100), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Weekly view", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "weekly", "2022-04-29T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 135.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 135.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 100, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(100), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Monthly view", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "monthly", "2022-04-29T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 135.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 135.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 100, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(100), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})
				})

				t.Run("Has first shift on 2022-05-05", func(t *testing.T) {
					// Given
					container := ittest.NewContainer(t)
					sim := newAttrDataSimulator(container, t)
					sim.SetSystemTime("2022-05-05T21:00:00+07:00")
					defer sim.UnfreezeTime()

					driverID := "driver-A"
					driver := sim.CreateDriver(driverID)
					sim.DriverAcceptOrder(driverID)

					// Without attendance stats because we had run script to delete all attendance stat before 2022-04-21
					shift1 := sim.AddShift("2022-04-14T08:30:00+07:00", "2022-04-14T10:00:00+07:00", 10, true)
					sim.DriverBookShift(shift1, driver.DriverID)

					// Without attendance stats because we had run script to delete all attendance stat before 2022-04-21
					shift2 := sim.AddShift("2022-04-15T18:30:00+07:00", "2022-04-15T20:45:00+07:00", 20, true)
					sim.DriverBookShift(shift2, driver.DriverID)

					// Has shift on 05-MAY
					shift3 := sim.AddShift("2022-05-05T18:30:00+07:00", "2022-05-05T20:45:00+07:00", 20, true)
					sim.DriverBookShift(shift3, driver.DriverID)
					sim.DriverAttendShift(driver.DriverID, shift3, []string{"18.30-20.45"})

					// When
					t.Run("Daily view on 2022-04-14", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-14T17:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 0.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 0, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(0), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Daily view on 2022-04-15", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-15T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 0.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 0, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(0), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Daily view on 2022-05-05", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-05-05T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 135.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 135.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 100, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(100), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Weekly view", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "weekly", "2022-05-05T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 135.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 135.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 100, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(100), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})

					t.Run("Monthly view", func(t *testing.T) {
						gctx := getPerformance(container.RedisTokenStore, "driver-A", "monthly", "2022-05-05T21:00:00+07:00")
						container.GinEngineRouter.HandleContext(gctx.GinCtx())

						// Then
						var resp account.DriverPerformanceResponse
						gctx.DecodeJSONResponse(&resp)

						gctx.AssertResponseCode(t, http.StatusOK)
						require.NotNil(t, resp.ATTR)
						assert.Equal(t, 135.0, resp.ATTR.TotalAttendingTime)
						assert.Equal(t, 135.0, resp.ATTR.TotalBookingTime)
						assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
						assert.Equal(t, 100, resp.ATTR.PercentageChange)
						assert.Equal(t, float64(100), resp.ATTR.ATTR)
						assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
					})
				})
			})

			t.Run("Driver views weekly performance at the end of the weeks", func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-05-01T09:53:00+07:00")
				sim.SetTimeLocation(time.UTC)
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)

				previous1 := sim.AddShift("2022-04-21T11:00:00+00:00", "2022-04-21T14:59:59+00:00", 0, true)
				sim.DriverBookShift(previous1, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-21", previous1, 239.983333333333)

				previous2 := sim.AddShift("2022-04-22T10:00:00+00:00", "2022-04-22T13:59:59+00:00", 0, true)
				sim.DriverBookShift(previous2, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-22", previous2, 239.983333333333)

				previous3 := sim.AddShift("2022-04-23T03:00:00+00:00", "2022-04-23T10:59:59+00:00", 0, true)
				sim.DriverBookShift(previous3, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-23", previous3, 478.983333333333)

				previous4 := sim.AddShift("2022-04-23T11:00:00+00:00", "2022-04-23T14:59:59+00:00", 0, true)
				sim.DriverBookShift(previous4, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-23", previous4, 239.983333333333)

				previous5 := sim.AddShift("2022-04-24T02:00:00+00:00", "2022-04-24T09:59:59+00:00", 0, true)
				sim.DriverBookShift(previous5, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-24", previous5, 474.983333333333)

				previous6 := sim.AddShift("2022-04-24T11:00:00+00:00", "2022-04-24T14:59:59+00:00", 0, true)
				sim.DriverBookShift(previous6, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-24", previous6, 239.983333333333)

				current1 := sim.AddShift("2022-04-28T02:00:00+00:00", "2022-04-28T09:59:59+00:00", 0, true)
				sim.DriverBookShift(current1, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-28", current1, 448.983333333333)

				current2 := sim.AddShift("2022-04-30T02:00:00+00:00", "2022-04-30T09:59:59+00:00", 0, true)
				sim.DriverBookShift(current2, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-30", current2, 478.983333333333)

				current3 := sim.AddShift("2022-04-30T10:00:00+00:00", "2022-04-30T13:59:59+00:00", 0, true)
				sim.DriverBookShift(current3, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-30", current3, 238.983333333333)

				today1 := sim.AddShift("2022-05-01T02:00:00+00:00", "2022-05-01T09:59:59+00:00", 0, true)
				sim.DriverBookShift(today1, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, today1, []string{"01.59-10.00"})

				today2 := sim.AddShift("2022-05-01T10:00:00+00:00", "2022-05-01T13:59:59+00:00", 0, true)
				sim.DriverBookShift(today2, driver.DriverID)

				// When
				t.Run("Daily view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-05-01T09:53:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 479.98, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 479.98, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 0, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(100), resp.ATTR.ATTR)
					assert.Equal(t, float64(99.72), resp.ATTR.PreviousATTR)
				})

				t.Run("Weekly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "weekly", "2022-05-01T09:53:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 1646.93, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 1679.93, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 33.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, -2, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(98.04), resp.ATTR.ATTR)
					assert.Equal(t, float64(99.69), resp.ATTR.PreviousATTR)
				})

				t.Run("Monthly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "monthly", "2022-05-01T09:53:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 479.98, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 479.98, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 1, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(100), resp.ATTR.ATTR)
					assert.Equal(t, float64(98.75), resp.ATTR.PreviousATTR) // 3080.85 ÷ 3119.85
				})
			})

			t.Run("Driver views weekly performance at the end of the month", func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				sim := newAttrDataSimulator(container, t)
				sim.SetSystemTime("2022-04-30T09:53:00+07:00")
				sim.SetTimeLocation(time.UTC)
				defer sim.UnfreezeTime()

				driverID := "driver-A"
				driver := sim.CreateDriver(driverID)
				sim.DriverAcceptOrder(driverID)

				previous1 := sim.AddShift("2022-04-21T11:00:00+00:00", "2022-04-21T14:59:59+00:00", 0, true)
				sim.DriverBookShift(previous1, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-21", previous1, 239.983333333333)

				previous2 := sim.AddShift("2022-04-22T10:00:00+00:00", "2022-04-22T13:59:59+00:00", 0, true)
				sim.DriverBookShift(previous2, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-22", previous2, 239.983333333333)

				previous3 := sim.AddShift("2022-04-23T03:00:00+00:00", "2022-04-23T10:59:59+00:00", 0, true)
				sim.DriverBookShift(previous3, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-23", previous3, 478.983333333333)

				previous4 := sim.AddShift("2022-04-23T11:00:00+00:00", "2022-04-23T14:59:59+00:00", 0, true)
				sim.DriverBookShift(previous4, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-23", previous4, 239.983333333333)

				previous5 := sim.AddShift("2022-04-24T02:00:00+00:00", "2022-04-24T09:59:59+00:00", 0, true)
				sim.DriverBookShift(previous5, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-24", previous5, 474.983333333333)

				previous6 := sim.AddShift("2022-04-24T11:00:00+00:00", "2022-04-24T14:59:59+00:00", 0, true)
				sim.DriverBookShift(previous6, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-24", previous6, 239.983333333333)

				current1 := sim.AddShift("2022-04-28T02:00:00+00:00", "2022-04-28T09:59:59+00:00", 0, true)
				sim.DriverBookShift(current1, driver.DriverID)
				sim.DriverAttendanceStat(driverID, "2022-04-28", current1, 448.983333333333)

				today1 := sim.AddShift("2022-04-30T02:00:00+00:00", "2022-04-30T09:59:59+00:00", 0, true)
				sim.DriverBookShift(today1, driver.DriverID)
				sim.DriverAttendShift(driver.DriverID, today1, []string{"01.59-10.00"})

				today2 := sim.AddShift("2022-04-30T10:00:00+00:00", "2022-04-30T13:59:59+00:00", 0, true)
				sim.DriverBookShift(today2, driver.DriverID)

				// When
				t.Run("Daily view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "daily", "2022-04-30T09:53:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 479.98, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 479.98, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 0.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 100, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(100), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})

				t.Run("Weekly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "weekly", "2022-04-30T09:53:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 928.97, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 959.97, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 31.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, -3, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(96.77), resp.ATTR.ATTR)
					assert.Equal(t, float64(99.69), resp.ATTR.PreviousATTR)
				})

				t.Run("Monthly view", func(t *testing.T) {
					gctx := getPerformance(container.RedisTokenStore, "driver-A", "monthly", "2022-04-30T09:53:00+07:00")
					container.GinEngineRouter.HandleContext(gctx.GinCtx())

					// Then
					var resp account.DriverPerformanceResponse
					gctx.DecodeJSONResponse(&resp)

					gctx.AssertResponseCode(t, http.StatusOK)
					require.NotNil(t, resp.ATTR)
					assert.Equal(t, 2842.87, resp.ATTR.TotalAttendingTime)
					assert.Equal(t, 2879.87, resp.ATTR.TotalBookingTime)
					assert.Equal(t, 37.0, resp.ATTR.TotalExceedQuotaBreakTime)
					assert.Equal(t, 99, resp.ATTR.PercentageChange)
					assert.Equal(t, float64(98.72), resp.ATTR.ATTR)
					assert.Equal(t, float64(0), resp.ATTR.PreviousATTR)
				})
			})
		})
	})
}

func TestAccountAPI_UpdateDriverLocation(t *testing.T) {
	t.Setenv("ENABLE_PUBLISH_UPDATE_LOCATION", "true")
	t.Setenv("UPDATE_LOCATION_TOPIC", "test-topic")

	testcases := []struct {
		name     string
		driverID string
		expect   struct {
			food      bool
			mart      bool
			messenger bool
			bike      bool
		}
	}{
		{
			name:     "feature online hours by service type: should publish message with service preference = food&mart, messenger, bike when rider service preference = food&mart, messenger, bike and v1/account/location was called",
			driverID: "DRV_ALL_SERVICES",
			expect: struct {
				food      bool
				mart      bool
				messenger bool
				bike      bool
			}{
				food:      true,
				mart:      true,
				messenger: true,
				bike:      true,
			},
		},
		{
			name:     "should publish message with service preference = food&mart when rider service preference = food&mart and v1/account/location was called",
			driverID: "DRV_FOOD_MART",
			expect: struct {
				food      bool
				mart      bool
				messenger bool
				bike      bool
			}{
				food:      true,
				mart:      true,
				messenger: false,
				bike:      false,
			},
		},
		{
			name:     "should publish message with service preference = messenger when rider service preference = messenger and v1/account/location was called",
			driverID: "DRV_MESSENGER",
			expect: struct {
				food      bool
				mart      bool
				messenger bool
				bike      bool
			}{
				food:      false,
				mart:      false,
				messenger: true,
				bike:      false,
			},
		},
		{
			name:     "should publish message with service preference = bike when rider service preference = bike and v1/account/location was called",
			driverID: "DRV_BIKE",
			expect: struct {
				food      bool
				mart      bool
				messenger bool
				bike      bool
			}{
				food:      false,
				mart:      false,
				messenger: false,
				bike:      true,
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			ctn := ittest.NewContainer(t)
			err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_update_location")
			require.NoError(t, err, "unable to setup fixture")

			ctn.SimpleUnleash.SetEnabled(featureflag.ServicePreferenceAllowedTypesForSelection.Name, true)
			ctn.SimpleUnleash.SetVariantWithPayloadValue(
				featureflag.ServicePreferenceAllowedTypesForSelection.Name,
				`["food","mart","messenger","bike"]`,
			)

			driverID := tc.driverID
			gctx := testutil.NewContextWithRecorder()

			gctx.SetPUT("/v1/account/location")
			gctx.Body().JSON(account.UpdateDriverLocationRequest{
				Lat:    1,
				Lng:    1,
				Region: "UPDATE_LOCATION_AREA",
			}).Build()
			gctx.Authorized(ctn.RedisTokenStore, driverID)

			gctx.Send(ctn.GinEngineRouter)
			gctx.AssertResponseCode(t, http.StatusNoContent)

			require.Len(t, ctn.IMFKafkaProducerClientForTest.Recorder, 1)

			event := ctn.IMFKafkaProducerClientForTest.Recorder[0]
			require.Equal(t, "test-topic", event.Topic)

			var u driverv1pb.UpdateLocationEvent
			err = proto.Unmarshal(event.Payload, &u)
			require.NoError(t, err)

			require.Len(t, u.AvailableCapacity, 4)

			food, mart, messenger, bike := u.AvailableCapacity[0], u.AvailableCapacity[1], u.AvailableCapacity[2], u.AvailableCapacity[3]

			require.Equal(t, "food", food.ServiceType)
			require.Equal(t, tc.expect.food, food.Enabled)

			require.Equal(t, "mart", mart.ServiceType)
			require.Equal(t, tc.expect.mart, mart.Enabled)

			require.Equal(t, "messenger", messenger.ServiceType)
			require.Equal(t, tc.expect.messenger, messenger.Enabled)

			require.Equal(t, "bike", bike.ServiceType)
			require.Equal(t, tc.expect.bike, bike.Enabled)
		})
	}
}

type attrDataSimulator struct {
	t                   *testing.T
	shiftDataStore      persistence.ShiftDataStore
	shiftRepo           repository.ShiftRepository
	driversDataStore    persistence.DriversDataStore
	driverRepo          repository.DriverRepository
	driverOrderInfoRepo repository.DriverOrderInfoRepository
	location            *time.Location
}

func newAttrDataSimulator(container *ittest.IntegrationTestContainer, t *testing.T) *attrDataSimulator {
	return &attrDataSimulator{
		t:                   t,
		shiftDataStore:      container.ShiftDataStore,
		shiftRepo:           container.ShiftRepository,
		driversDataStore:    container.DriversDataStore,
		driverRepo:          container.DriverRepository,
		driverOrderInfoRepo: container.DriverOrderInfoRepository,
		location:            timeutils.BangkokLocation(),
	}
}

func (a *attrDataSimulator) SetSystemTime(s string) {
	t, err := time.Parse(time.RFC3339, s)
	require.NoError(a.t, err, "parse system time error")

	timeutils.Now = func() time.Time {
		return t
	}
}

func (a *attrDataSimulator) SetTimeLocation(location *time.Location) {
	a.location = location
}

func (a *attrDataSimulator) UnfreezeTime() {
	timeutils.Unfreeze()
}

func (a *attrDataSimulator) CreateDriver(driverID string) model.Driver {
	newDriver := model.Driver{
		DriverID: driverID,
	}

	err := a.driverRepo.Create(context.Background(), &newDriver)
	require.NoError(a.t, err, "create driver error")

	driver, err := a.driverRepo.FindDriverID(context.Background(), driverID)

	require.NoError(a.t, err, "find driver error")
	return *driver
}

func (a *attrDataSimulator) DriverAcceptOrder(driverID string) model.DriverOrderInfo {
	doi, err := a.driverOrderInfoRepo.GetOrCreate(context.Background(), driverID)

	require.NoError(a.t, err, "driver accept order error")
	return *doi
}

func (a *attrDataSimulator) AddShift(start string, end string, breakTimeQuota float64, active bool) model.Shift {
	startTime, err := time.Parse(time.RFC3339, start)
	require.NoError(a.t, err, "parse start time error")

	endTime, err := time.Parse(time.RFC3339, end)
	require.NoError(a.t, err, "parse end time error")

	newShift := model.Shift{
		ID:         primitive.NewObjectID(),
		Active:     active,
		BreakQuota: breakTimeQuota,
		Start:      startTime,
		End:        endTime,
	}

	err = a.shiftRepo.CreateAll(context.Background(), []model.Shift{
		newShift,
	})

	require.NoError(a.t, err, "add shift error")

	shift, err := a.shiftRepo.FindByID(context.Background(), newShift.ID.Hex())
	require.NoError(a.t, err, "find shift error")

	return shift
}

func (a *attrDataSimulator) DriverBookShift(shift model.Shift, driverID string) {
	err := a.shiftRepo.Book(context.Background(), &shift, driverID)
	require.NoError(a.t, err, "book shift error")
}

func (a *attrDataSimulator) DriverAttendShift(driverID string, shift model.Shift, working []string) {
	datetime := func(dt time.Time, ts string) time.Time {
		t, _ := time.Parse("15.04", ts)
		return time.Date(dt.Year(), dt.Month(), dt.Day(), t.Hour(), t.Minute(), 0, 0, a.location)
	}

	attendanceLogs := []model.AttendanceLog{}
	for _, w := range working {
		tt := strings.Split(w, "-")
		workingFrom := tt[0]
		workingTo := tt[1]

		attendanceLogs = append(attendanceLogs, []model.AttendanceLog{
			{
				Status: model.AttendanceStatusOnline,
				Time:   datetime(shift.Start, workingFrom),
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   datetime(shift.Start, workingTo),
			},
		}...)
	}

	err := a.driverRepo.AddAttendanceLog(context.Background(), driverID, attendanceLogs)
	require.NoError(a.t, err, "add attendance log error")
}

func (a *attrDataSimulator) DriverAttendanceStat(driverID string, date string, shift model.Shift, actual float64) {
	doi, err := a.driverOrderInfoRepo.FindDriverAttendanceStat(context.Background(), driverID)
	require.NoError(a.t, err, "find driver attendance stat error")

	var attendances []model.AttendanceTime
	v, exist := doi.AttendanceStat[date]
	if exist {
		attendances = v
	}

	attendances = append(attendances, model.AttendanceTime{
		ShiftId:   shift.ID.Hex(),
		Actual:    actual,
		ShiftTime: shift.End.Sub(shift.Start).Minutes(),
	})

	atds := model.AttendanceStat{
		date: attendances,
	}

	err = a.driverOrderInfoRepo.AddAttendanceStat(context.Background(), driverID, atds)
	require.NoError(a.t, err, "add attendance stat error")
}

func getIncomeSummary(tokenStore auth.TokenStorage, driverID string, granularity string, date string, incomeType string, optionals ...func(params *url.Values)) *testutil.GinContextWithRecorder {
	gctx := testutil.NewContextWithRecorder()
	gctx.SetGET("/v1/account/income-summary")

	params := url.Values{}
	if granularity != "" {
		params.Add("granularity", granularity)
	}

	if date != "" {
		params.Add("date", date)
	}

	if incomeType != "" {
		params.Add("type", incomeType)
	}

	for _, v := range optionals {
		v(&params)
	}

	gctx.SetQuery(params.Encode())

	gctx.Authorized(tokenStore, driverID)

	return gctx
}

func TestAccountAPI_GetIncomeSummary_Happy_Cases(t *testing.T) {
	t.Parallel()

	t.Run("new driver complete food order", func(t *testing.T) {
		// Given
		ctn := ittest.NewContainer(t)
		err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_income_summary/scenarios/driver_complete_food_order")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view daily", func(t *testing.T) {
			gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "daily", "2022-12-13T05:02:06.135Z", "")
			timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()))
			gctx.Send(ctn.GinEngineRouter)

			// Then
			require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
			var actual account.IncomeSummaryResponse
			testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

			assert.Equal(t, types.NewMoney(41.22), actual.Summary.TotalIncome)
			assert.Equal(t, types.NewMoney(41.22), actual.Summary.Changes)
			assert.Equal(t, types.NewMoney(42.5), actual.Summary.TotalWage)
			assert.Equal(t, types.NewMoney(1.28), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, 1, actual.Summary.TotalTrip)
			assert.Equal(t, 1, actual.Summary.TotalOrder)
		})

		// When
		t.Run("view weekly", func(t *testing.T) {
			gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "weekly", "2022-12-13T05:02:06.135Z", "")
			timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()))
			gctx.Send(ctn.GinEngineRouter)

			// Then
			require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
			var actual account.IncomeSummaryResponse
			testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

			assert.Equal(t, types.NewMoney(41.22), actual.Summary.TotalIncome)
			assert.Equal(t, types.NewMoney(41.22), actual.Summary.Changes)
			assert.Equal(t, types.NewMoney(42.5), actual.Summary.TotalWage)
			assert.Equal(t, types.NewMoney(1.28), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, 1, actual.Summary.TotalTrip)
			assert.Equal(t, 1, actual.Summary.TotalOrder)
		})

		// When
		t.Run("view monthly", func(t *testing.T) {
			gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "monthly", "2022-12-13T05:02:06.135Z", "")
			timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()))
			gctx.Send(ctn.GinEngineRouter)

			// Then
			require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
			var actual account.IncomeSummaryResponse
			testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

			assert.Equal(t, types.NewMoney(41.22), actual.Summary.TotalIncome)
			assert.Equal(t, types.NewMoney(41.22), actual.Summary.Changes)
			assert.Equal(t, types.NewMoney(42.5), actual.Summary.TotalWage)
			assert.Equal(t, types.NewMoney(1.28), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, 1, actual.Summary.TotalTrip)
			assert.Equal(t, 1, actual.Summary.TotalOrder)
		})
	})

	t.Run("new driver complete messenger cash order w/o incentive and tip", func(t *testing.T) {
		t.Parallel()
		// Given
		ctn := ittest.NewContainer(t)
		err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_income_summary/scenarios/driver_complete_messenger_order")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view daily", func(t *testing.T) {
			gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "daily", "2022-12-13T05:02:06.135Z", "")
			timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()))
			gctx.Send(ctn.GinEngineRouter)

			// Then
			require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
			var actual account.IncomeSummaryResponse
			testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

			assert.Equal(t, types.NewMoney(36.12), actual.Summary.TotalIncome)
			assert.Equal(t, types.NewMoney(36.12), actual.Summary.Changes)
			assert.Equal(t, types.NewMoney(36.12), actual.Summary.TotalWage)
			assert.Equal(t, types.NewMoney(0), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, 1, actual.Summary.TotalTrip)
			assert.Equal(t, 1, actual.Summary.TotalOrder)
		})

		// When
		t.Run("view weekly", func(t *testing.T) {
			gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "weekly", "2022-12-13T05:02:06.135Z", "")
			timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()))
			gctx.Send(ctn.GinEngineRouter)

			// Then
			require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
			var actual account.IncomeSummaryResponse
			testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

			assert.Equal(t, types.NewMoney(36.12), actual.Summary.TotalIncome)
			assert.Equal(t, types.NewMoney(36.12), actual.Summary.Changes)
			assert.Equal(t, types.NewMoney(36.12), actual.Summary.TotalWage)
			assert.Equal(t, types.NewMoney(0), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, 1, actual.Summary.TotalTrip)
			assert.Equal(t, 1, actual.Summary.TotalOrder)
		})

		// When
		t.Run("view monthly", func(t *testing.T) {
			gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "monthly", "2022-12-13T05:02:06.135Z", "")
			timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()))
			gctx.Send(ctn.GinEngineRouter)

			// Then
			require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
			var actual account.IncomeSummaryResponse
			testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

			assert.Equal(t, types.NewMoney(36.12), actual.Summary.TotalIncome)
			assert.Equal(t, types.NewMoney(36.12), actual.Summary.Changes)
			assert.Equal(t, types.NewMoney(36.12), actual.Summary.TotalWage)
			assert.Equal(t, types.NewMoney(0), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, 1, actual.Summary.TotalTrip)
			assert.Equal(t, 1, actual.Summary.TotalOrder)
		})
	})

	t.Run("driver complete mart order with tip", func(t *testing.T) {
		t.Parallel()
		// Given
		ctn := ittest.NewContainer(t)
		err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_income_summary/scenarios/driver_complete_mart_order_with_tip/before")
		require.NoError(t, err, "unable to setup fixture")

		t.Run("before", func(t *testing.T) {
			// When
			t.Run("view daily", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "daily", "2022-12-19T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 19, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(0), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(-194), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(0), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(0), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(0), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(0), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 0, actual.Summary.TotalTrip)
				assert.Equal(t, 0, actual.Summary.TotalOrder)
			})

			// When
			t.Run("view weekly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "weekly", "2022-12-13T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 19, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(504.40), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(324.80), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(500), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(20), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(15.60), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(0), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 5, actual.Summary.TotalTrip)
				assert.Equal(t, 5, actual.Summary.TotalOrder)
			})

			// When
			t.Run("view monthly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "monthly", "2022-12-13T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 19, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(781), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(781), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(770), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(30), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(24), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(5), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 9, actual.Summary.TotalTrip)
				assert.Equal(t, 9, actual.Summary.TotalOrder)
			})
		})

		t.Run("after", func(t *testing.T) {
			err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_income_summary/scenarios/driver_complete_mart_order_with_tip/after")
			require.NoError(t, err, "unable to setup fixture")

			// When
			t.Run("view daily", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "daily", "2022-12-19T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 19, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(56.22), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(-137.78), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(42.5), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(0), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(1.28), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(15), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 1, actual.Summary.TotalTrip)
			})

			// When
			t.Run("view weekly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "weekly", "2022-12-13T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 19, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(504.40), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(324.80), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(500), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(20), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(15.60), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(0), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 5, actual.Summary.TotalTrip)
			})

			// When
			t.Run("view monthly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "monthly", "2022-12-13T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 19, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(837.22), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(837.22), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(812.50), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(30), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(25.28), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(20), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 10, actual.Summary.TotalTrip)
			})
		})
	})

	t.Run("driver complete MO (2 orders) 1 trip with on top and tip", func(t *testing.T) {
		t.Parallel()
		// Given
		ctn := ittest.NewContainer(t)
		err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_income_summary/scenarios/driver_complete_mo_(2_orders)_1_trip_with_on_top_and_tip/before")
		require.NoError(t, err, "unable to setup fixture")

		t.Run("before", func(t *testing.T) {
			// When
			t.Run("view daily", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "daily", "2022-12-15T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 15, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(859.05), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(456.5), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(850), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(15), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(25.95), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(20), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 3, actual.Summary.TotalTrip)
				assert.Equal(t, 3, actual.Summary.TotalOrder)
			})

			// When
			t.Run("view weekly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "weekly", "2022-12-13T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 15, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(1740.35), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(-12.50), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(1700), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(55), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(52.65), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(38), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 9, actual.Summary.TotalTrip)
				assert.Equal(t, 12, actual.Summary.TotalOrder)
			})

			// When
			t.Run("view monthly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "monthly", "2022-12-13T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 15, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(3687.20), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(292.20), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(3700), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(60.00), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(112.80), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(40), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 12, actual.Summary.TotalTrip)
				assert.Equal(t, 15, actual.Summary.TotalOrder)
			})
		})

		t.Run("after", func(t *testing.T) {
			err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_income_summary/scenarios/driver_complete_mo_(2_orders)_1_trip_with_on_top_and_tip/after")
			require.NoError(t, err, "unable to setup fixture")

			// When
			t.Run("view daily", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "daily", "2022-12-15T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 15, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(951.01), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(548.46), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(929.50), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(20), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(28.49), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(30), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 4, actual.Summary.TotalTrip)
				assert.Equal(t, 5, actual.Summary.TotalOrder)
			})

			// When
			t.Run("view weekly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "weekly", "2022-12-13T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 15, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(1832.31), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(79.46), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(1779.50), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(60), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(55.19), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(48), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 10, actual.Summary.TotalTrip)
				assert.Equal(t, 14, actual.Summary.TotalOrder)
			})

			// When
			t.Run("view monthly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "monthly", "2022-12-13T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 15, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(3779.16), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(384.16), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(3779.50), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(65.00), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(115.34), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(50), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 13, actual.Summary.TotalTrip)
				assert.Equal(t, 17, actual.Summary.TotalOrder)
			})
		})
	})

	t.Run("driver complete messenger order with delivery coupon", func(t *testing.T) {
		t.Parallel()

		// Given
		ctn := ittest.NewContainer(t)
		err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_income_summary/scenarios/driver_complete_messenger_order_with_delivery_coupon/before")
		require.NoError(t, err, "unable to setup fixture")

		t.Run("before", func(t *testing.T) {
			// When
			t.Run("view daily", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "daily", "2022-12-23T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 23, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(534.40), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(214.30), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(510.00), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(10), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(15.60), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(30), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 3, actual.Summary.TotalTrip)
				assert.Equal(t, 3, actual.Summary.TotalOrder)
			})

			// When
			t.Run("view weekly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "weekly", "2022-12-23T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 23, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(1058.35), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(-96.10), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(1000.00), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(55.00), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(31.65), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(35), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 7, actual.Summary.TotalTrip)
				assert.Equal(t, 7, actual.Summary.TotalOrder)
			})

			// When
			t.Run("view monthly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "monthly", "2022-12-13T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 23, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(2212.80), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(466.80), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(2180.00), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(60.00), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(67.20), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(40.00), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 8, actual.Summary.TotalTrip)
			})
		})

		t.Run("after", func(t *testing.T) {
			err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_income_summary/scenarios/driver_complete_messenger_order_with_delivery_coupon/after")
			require.NoError(t, err, "unable to setup fixture")

			// When
			t.Run("view daily", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "daily", "2022-12-23T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 23, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(593.90), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(273.80), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(569.50), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(10.00), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(15.60), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(30), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 4, actual.Summary.TotalTrip)
				assert.Equal(t, 4, actual.Summary.TotalOrder)
			})

			// When
			t.Run("view weekly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "weekly", "2022-12-23T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 23, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(1117.85), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(-36.60), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(1059.50), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(55), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(31.65), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(35.00), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 8, actual.Summary.TotalTrip)
				assert.Equal(t, 8, actual.Summary.TotalOrder)
			})

			// When
			t.Run("view monthly", func(t *testing.T) {
				gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "monthly", "2022-12-13T05:02:06.135Z", "")
				timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 23, 20, 0, 0, 0, timeutil.BangkokLocation()))
				gctx.Send(ctn.GinEngineRouter)

				// Then
				require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
				var actual account.IncomeSummaryResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

				assert.Equal(t, types.NewMoney(2272.30), actual.Summary.TotalIncome)
				assert.Equal(t, types.NewMoney(526.30), actual.Summary.Changes)
				assert.Equal(t, types.NewMoney(2239.50), actual.Summary.TotalWage)
				assert.Equal(t, types.NewMoney(60.00), actual.Summary.TotalIncentive)
				assert.Equal(t, types.NewMoney(67.20), actual.Summary.TotalWithholdingTax)
				assert.Equal(t, types.NewMoney(40), actual.Summary.TotalTipAndOthers)
				assert.Equal(t, 9, actual.Summary.TotalTrip)
				assert.Equal(t, 9, actual.Summary.TotalOrder)
			})
		})
	})

	t.Run("should be able to view 8 days ago", func(t *testing.T) {
		t.Parallel()

		// Given
		ctn := ittest.NewContainer(t)
		err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_income_summary/scenarios/view_eight_day_ago")
		require.NoError(t, err, "unable to setup fixture")

		// When
		gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "last_8_day_ago", "2022-12-08T05:00:00.000Z", "")
		timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 8, 11, 0, 0, 0, timeutil.BangkokLocation()))
		gctx.Send(ctn.GinEngineRouter)

		// Then
		require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
		var actual account.IncomeSummaryResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

		assert.Nil(t, actual.Summary)
		require.NotNil(t, actual.Overview)
		require.Len(t, actual.Overview.Incomes, 8)

		date := time.Date(2022, 12, 1, 0, 0, 0, 0, timeutil.BangkokLocation())
		for _, income := range actual.Overview.Incomes {
			assert.Equal(t, date, income.Date.In(timeutil.BangkokLocation()))
			assert.Equal(t, types.Money(41.22), income.TotalIncome)
			date = date.AddDate(0, 0, 1)
		}
	})
}

func TestAccountAPI_GetIncomeSummary_Fail_Cases(t *testing.T) {
	t.Parallel()

	// Given
	ctn := ittest.NewContainer(t)

	// When
	t.Run("error invalid granularity", func(t *testing.T) {
		gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "invalid", "", "")
		gctx.Send(ctn.GinEngineRouter)

		// Then
		require.Equal(t, http.StatusBadRequest, gctx.ResponseRecorder.Code)
		var actual api.Error
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)
		assert.Equal(t, "INVALID_REQUEST", actual.Code)
		assert.Equal(t, "invalid granularity", actual.Message)
	})

	// When
	t.Run("error when request to view data older than config", func(t *testing.T) {
		gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDSAC9I3", "monthly", "2022-05-13T05:02:06.135Z", "")
		timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()))
		gctx.Send(ctn.GinEngineRouter)

		// Then
		require.Equal(t, http.StatusBadRequest, gctx.ResponseRecorder.Code)
		var actual api.Error
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)
		assert.Equal(t, "INVALID_REQUEST", actual.Code)
		assert.Equal(t, "cannot select historical month less than 6 months", actual.Message)
	})
}

func TestAccountAPI_GetIncomeSummary_Prod_Cases(t *testing.T) {
	t.Parallel()
	t.Run("messenger with on-top", func(t *testing.T) {
		t.Parallel()

		// Given
		ctn := ittest.NewContainer(t)
		err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_income_summary/scenarios/prod_issue_messenger_with_ontop")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view daily", func(t *testing.T) {
			t.Parallel()

			gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDKG0JIX", "daily", "2023-01-26T05:02:06.135Z", "")
			timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2023, 1, 26, 11, 0, 0, 0, timeutil.BangkokLocation()))
			gctx.Send(ctn.GinEngineRouter)

			// Then
			require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
			var actual account.IncomeSummaryResponse
			testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

			assert.Equal(t, types.NewMoney(1882.4), actual.Summary.TotalIncome)
			assert.Equal(t, types.NewMoney(1882.4), actual.Summary.Changes)
			assert.Equal(t, types.NewMoney(1844.71), actual.Summary.TotalWage)
			assert.Equal(t, types.NewMoney(47.35), actual.Summary.TotalIncentive)
			assert.Equal(t, types.NewMoney(0), actual.Summary.TotalTipAndOthers)
			assert.Equal(t, types.NewMoney(9.66), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, 11, actual.Summary.TotalTrip)
			assert.Equal(t, 11, actual.Summary.TotalOrder)
		})

		// When
		t.Run("view weekly", func(t *testing.T) {
			t.Parallel()

			gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDKG0JIX", "weekly", "2023-01-26T05:02:06.135Z", "")
			timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2023, 1, 26, 11, 0, 0, 0, timeutil.BangkokLocation()))
			gctx.Send(ctn.GinEngineRouter)

			// Then
			require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
			var actual account.IncomeSummaryResponse
			testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

			assert.Equal(t, types.NewMoney(1882.4), actual.Summary.TotalIncome)
			assert.Equal(t, types.NewMoney(1882.4), actual.Summary.Changes)
			assert.Equal(t, types.NewMoney(1844.71), actual.Summary.TotalWage)
			assert.Equal(t, types.NewMoney(47.35), actual.Summary.TotalIncentive)
			assert.Equal(t, types.NewMoney(0), actual.Summary.TotalTipAndOthers)
			assert.Equal(t, types.NewMoney(9.66), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, 11, actual.Summary.TotalTrip)
			assert.Equal(t, 11, actual.Summary.TotalOrder)
		})

		// When
		t.Run("view monthly", func(t *testing.T) {
			t.Parallel()

			gctx := getIncomeSummary(ctn.RedisTokenStore, "LMDKG0JIX", "daily", "2023-01-26T05:02:06.135Z", "")
			timeutil.NewGinContextWithTime(gctx.GinCtx(), time.Date(2023, 1, 26, 11, 0, 0, 0, timeutil.BangkokLocation()))
			gctx.Send(ctn.GinEngineRouter)

			// Then
			require.Equal(t, http.StatusOK, gctx.ResponseRecorder.Code)
			var actual account.IncomeSummaryResponse
			testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)

			assert.Equal(t, types.NewMoney(1882.4), actual.Summary.TotalIncome)
			assert.Equal(t, types.NewMoney(1882.4), actual.Summary.Changes)
			assert.Equal(t, types.NewMoney(1844.71), actual.Summary.TotalWage)
			assert.Equal(t, types.NewMoney(47.35), actual.Summary.TotalIncentive)
			assert.Equal(t, types.NewMoney(0), actual.Summary.TotalTipAndOthers)
			assert.Equal(t, types.NewMoney(9.66), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, 11, actual.Summary.TotalTrip)
			assert.Equal(t, 11, actual.Summary.TotalOrder)
		})
	})
}

func TestAccountAPI_GetMyInsurance(t *testing.T) {
	container := ittest.NewContainer(t)

	if err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_driver_insurances"); err != nil {
		t.Errorf("unexpected error at InitFixture: %v", err)
	}

	newUpdateDriverStatusHandlerRequest := func(tokenStore auth.TokenStorage, driverID string) *testutil.GinContextWithRecorder {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/my-insurances")
		gctx.Authorized(tokenStore, driverID)
		return gctx
	}

	t.Run("no existing/previous insurance", func(tt *testing.T) {
		timeutils.FreezeWithTime(time.Date(2025, 1, 1, 0, 0, 0, 0, timeutil.BangkokLocation()).UnixMilli())
		defer timeutils.Unfreeze()

		gctx := newUpdateDriverStatusHandlerRequest(container.RedisTokenStore, "NO_DRIVER_FOUND")

		container.StubGRPCFormService.EXPECT().
			ListForm(gctx.GinCtx(), gomock.Any()).Return(&formServicePb.ListFormResponse{
			Data: []*formServicePb.Form{},
		}, nil)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(tt, http.StatusOK)

		var actualResponse account.DriverMyInsuranceResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actualResponse)

		exptResponse := account.DriverMyInsuranceResponse{
			Insurances: []account.DriverInsuranceResponse{
				{
					Type:           "personal_accident",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันอุบัติเหตุ",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "",
					Message:        "ประกันอุบัติเหตุพื้นฐานคุ้มครองทุกการทำงาน ที่ไลน์แมนมอบให้ไรเดอร์ทุกคนโดยไม่มีค่าใช้จ่าย",
					RegisteredDate: "0001-01-01T00:00:00.000Z",
					StartDate:      "0001-01-01T00:00:00.000Z",
					EndDate:        "0001-01-01T00:00:00.000Z",
				},
			},
		}

		assert.Equal(tt, exptResponse, actualResponse)
	})

	t.Run("all new insurance is Chubb", func(tt *testing.T) {
		timeutils.FreezeWithTime(time.Date(2025, 3, 1, 0, 0, 0, 0, timeutil.BangkokLocation()).UnixMilli())
		defer timeutils.Unfreeze()

		gctx := newUpdateDriverStatusHandlerRequest(container.RedisTokenStore, "DRV_PATTAYA_ONLINE")

		container.StubGRPCFormService.EXPECT().
			ListForm(gctx.GinCtx(), gomock.Any()).Return(&formServicePb.ListFormResponse{
			Data: []*formServicePb.Form{},
		}, nil)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(tt, http.StatusOK)

		var actualResponse account.DriverMyInsuranceResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actualResponse)

		exptResponse := account.DriverMyInsuranceResponse{
			Insurances: []account.DriverInsuranceResponse{
				{
					Type:           "motorcycle",
					Status:         account.InsuranceStatus("WAITING_FOR_CONFIRM"),
					Title:          "ประกันรถจักรยานยนต์",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "DRV_PATTAYA_ONLINE_FIRST_NAME DRV_PATTAYA_ONLINE_LAST_NAME",
					Message:        "",
					RegisteredDate: "2025-03-01T07:00:00.000Z",
					StartDate:      "2025-03-14T17:01:00.000Z",
					EndDate:        "2025-04-15T09:30:00.000Z",
				},
				{
					Type:           "personal_accident",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันอุบัติเหตุ",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "",
					Message:        "ประกันอุบัติเหตุพื้นฐานคุ้มครองทุกการทำงาน ที่ไลน์แมนมอบให้ไรเดอร์ทุกคนโดยไม่มีค่าใช้จ่าย",
					RegisteredDate: "0001-01-01T00:00:00.000Z",
					StartDate:      "0001-01-01T00:00:00.000Z",
					EndDate:        "0001-01-01T00:00:00.000Z",
				},
			},
		}

		assert.Equal(tt, exptResponse, actualResponse)
	})

	t.Run("already have a Rabbit insurance and the insurance will open soon", func(tt *testing.T) {
		timeutils.FreezeWithTime(time.Date(2025, 2, 28, 0, 0, 0, 0, timeutil.BangkokLocation()).UnixMilli())
		defer timeutils.Unfreeze()

		gctx := newUpdateDriverStatusHandlerRequest(container.RedisTokenStore, "DRV_CHIANG_MAI_2_ONLINE")

		container.StubGRPCFormService.EXPECT().
			ListForm(gctx.GinCtx(), gomock.Any()).Return(&formServicePb.ListFormResponse{
			Data: []*formServicePb.Form{},
		}, nil)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(tt, http.StatusOK)

		var actualResponse account.DriverMyInsuranceResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actualResponse)

		exptResponse := account.DriverMyInsuranceResponse{
			Insurances: []account.DriverInsuranceResponse{
				{
					Type:           "motorcycle",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันรถจักรยานยนต์",
					Subtitle:       "Bangkok Insurance",
					Broker:         model.DriverInsuranceBroker("rabbit"),
					FullName:       "DRV_CHIANG_MAI_2_ONLINE_FIRST_NAME DRV_CHIANG_MAI_2_ONLINE_LAST_NAME",
					Message:        "",
					RegisteredDate: "2025-02-01T07:00:00.000Z",
					StartDate:      "2025-02-14T17:01:00.000Z",
					EndDate:        "2025-03-15T09:30:00.000Z",
					Option: account.DriverInsuranceOptionalResponse{
						Type:         account.DriverInsuranceOptionalType("EXPIRED_SOON"),
						Title:        "กรมธรรม์ของคุณใกล้หมดอายุแล้ว",
						Subtitle:     "ต่ออายุภายในวันที่ 1 มี.ค. - 4 มี.ค. 68",
						IsCTADisplay: false,
					},
				},
				{
					Type:           "personal_accident",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันอุบัติเหตุ",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "",
					Message:        "ประกันอุบัติเหตุพื้นฐานคุ้มครองทุกการทำงาน ที่ไลน์แมนมอบให้ไรเดอร์ทุกคนโดยไม่มีค่าใช้จ่าย",
					RegisteredDate: "0001-01-01T00:00:00.000Z",
					StartDate:      "0001-01-01T00:00:00.000Z",
					EndDate:        "0001-01-01T00:00:00.000Z",
				},
			},
		}

		assert.Equal(tt, exptResponse, actualResponse)
	})

	t.Run("already have a Rabbit insurance and the insurance currently open to register", func(tt *testing.T) {
		timeutils.FreezeWithTime(time.Date(2025, 3, 2, 0, 0, 0, 0, timeutil.BangkokLocation()).UnixMilli())
		defer timeutils.Unfreeze()

		gctx := newUpdateDriverStatusHandlerRequest(container.RedisTokenStore, "DRV_CHIANG_MAI_2_ONLINE")

		container.StubGRPCFormService.EXPECT().
			ListForm(gctx.GinCtx(), gomock.Any()).Return(&formServicePb.ListFormResponse{
			Data: []*formServicePb.Form{},
		}, nil)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(tt, http.StatusOK)

		var actualResponse account.DriverMyInsuranceResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actualResponse)

		exptResponse := account.DriverMyInsuranceResponse{
			Insurances: []account.DriverInsuranceResponse{
				{
					Type:           "motorcycle",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันรถจักรยานยนต์",
					Subtitle:       "Bangkok Insurance",
					Broker:         model.DriverInsuranceBroker("rabbit"),
					FullName:       "DRV_CHIANG_MAI_2_ONLINE_FIRST_NAME DRV_CHIANG_MAI_2_ONLINE_LAST_NAME",
					Message:        "",
					RegisteredDate: "2025-02-01T07:00:00.000Z",
					StartDate:      "2025-02-14T17:01:00.000Z",
					EndDate:        "2025-03-15T09:30:00.000Z",
					Option: account.DriverInsuranceOptionalResponse{
						Type:         account.DriverInsuranceOptionalType("EXPIRED_SOON"),
						Title:        "กรมธรรม์ของคุณใกล้หมดอายุแล้ว",
						Subtitle:     "ต่ออายุภายในวันที่ 1 มี.ค. - 4 มี.ค. 68",
						IsCTADisplay: true,
					},
				},
				{
					Type:           "personal_accident",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันอุบัติเหตุ",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "",
					Message:        "ประกันอุบัติเหตุพื้นฐานคุ้มครองทุกการทำงาน ที่ไลน์แมนมอบให้ไรเดอร์ทุกคนโดยไม่มีค่าใช้จ่าย",
					RegisteredDate: "0001-01-01T00:00:00.000Z",
					StartDate:      "0001-01-01T00:00:00.000Z",
					EndDate:        "0001-01-01T00:00:00.000Z",
				},
			},
		}

		assert.Equal(tt, exptResponse, actualResponse)
	})

	t.Run("already have a Rabbit insurance, currently open to register and already create a form", func(tt *testing.T) {
		timeutils.FreezeWithTime(time.Date(2025, 3, 3, 0, 0, 0, 0, timeutil.BangkokLocation()).UnixMilli())
		defer timeutils.Unfreeze()

		gctx := newUpdateDriverStatusHandlerRequest(container.RedisTokenStore, "DRV_CHIANG_MAI_2_ONLINE")

		formStruct, _ := structpb.NewStruct(map[string]any{
			"type":      "Motor", // ["PA", "Motor", "Motor+PA"]
			"firstName": "DRV_CHIANG_MAI_2_ONLINE_FIRST_NAME",
			"lastName":  "DRV_CHIANG_MAI_2_ONLINE_LAST_NAME",
		})

		container.StubGRPCFormService.EXPECT().
			ListForm(gctx.GinCtx(), gomock.Any()).Return(&formServicePb.ListFormResponse{
			Data: []*formServicePb.Form{
				{
					Id:          "FORM_ID03",
					RefId:       "DRIVER_ID",
					FormType:    formServicePb.FormType_FORM_TYPE_INSURANCE_REGISTRATION,
					FormSubtype: formServicePb.FormSubtype_FORM_SUBTYPE_NONE,
					Status:      formServicePb.FormStatus_FORM_STATUS_SUBMITED,
					Value:       formStruct,
					IssuedAt:    timestamppb.New(time.Date(2025, 3, 2, 17, 0, 0, 0, timeutil.BangkokLocation())),
					CreatedAt:   timestamppb.New(time.Date(2025, 3, 2, 18, 0, 0, 0, timeutil.BangkokLocation())),
				},
			},
		}, nil)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(tt, http.StatusOK)

		var actualResponse account.DriverMyInsuranceResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actualResponse)

		exptResponse := account.DriverMyInsuranceResponse{
			Insurances: []account.DriverInsuranceResponse{
				{
					Type:           "motorcycle",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันรถจักรยานยนต์",
					Subtitle:       "Bangkok Insurance",
					Broker:         model.DriverInsuranceBroker("rabbit"),
					FullName:       "DRV_CHIANG_MAI_2_ONLINE_FIRST_NAME DRV_CHIANG_MAI_2_ONLINE_LAST_NAME",
					Message:        "",
					RegisteredDate: "2025-02-01T07:00:00.000Z",
					StartDate:      "2025-02-14T17:01:00.000Z",
					EndDate:        "2025-03-15T09:30:00.000Z",
					Option: account.DriverInsuranceOptionalResponse{
						Type:         account.DriverInsuranceOptionalType("WAITING_FOR_CONFIRM"),
						Title:        "รออนุมัติการต่ออายุกรมธรรม์",
						Subtitle:     "กรุณารอผลภายใน 7 วันทำการ หลังวันที่ลงทะเบียน",
						IsCTADisplay: false,
					},
				},
				{
					Type:           "personal_accident",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันอุบัติเหตุ",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "",
					Message:        "ประกันอุบัติเหตุพื้นฐานคุ้มครองทุกการทำงาน ที่ไลน์แมนมอบให้ไรเดอร์ทุกคนโดยไม่มีค่าใช้จ่าย",
					RegisteredDate: "0001-01-01T00:00:00.000Z",
					StartDate:      "0001-01-01T00:00:00.000Z",
					EndDate:        "0001-01-01T00:00:00.000Z",
				},
			},
		}

		assert.Equal(tt, exptResponse, actualResponse)
	})

	t.Run("already have a Rabbit insurance, currently open to register, already create a form and the form rejected", func(tt *testing.T) {
		timeutils.FreezeWithTime(time.Date(2025, 3, 3, 0, 0, 0, 0, timeutil.BangkokLocation()).UnixMilli())
		defer timeutils.Unfreeze()

		gctx := newUpdateDriverStatusHandlerRequest(container.RedisTokenStore, "DRV_CHIANG_MAI_2_ONLINE")

		formStruct, _ := structpb.NewStruct(map[string]any{
			"type":           "Motor", // ["PA", "Motor", "Motor+PA"]
			"firstName":      "DRV_CHIANG_MAI_2_ONLINE_FIRST_NAME",
			"lastName":       "DRV_CHIANG_MAI_2_ONLINE_LAST_NAME",
			"rejectedReason": "added_rejected_reason",
		})

		container.StubGRPCFormService.EXPECT().
			ListForm(gctx.GinCtx(), gomock.Any()).Return(&formServicePb.ListFormResponse{
			Data: []*formServicePb.Form{
				{
					Id:          "FORM_ID03",
					RefId:       "DRIVER_ID",
					FormType:    formServicePb.FormType_FORM_TYPE_INSURANCE_REGISTRATION,
					FormSubtype: formServicePb.FormSubtype_FORM_SUBTYPE_NONE,
					Status:      formServicePb.FormStatus_FORM_STATUS_REJECTED,
					Value:       formStruct,
					IssuedAt:    timestamppb.New(time.Date(2025, 3, 2, 17, 0, 0, 0, timeutil.BangkokLocation())),
					CreatedAt:   timestamppb.New(time.Date(2025, 3, 2, 18, 0, 0, 0, timeutil.BangkokLocation())),
				},
			},
		}, nil)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(tt, http.StatusOK)

		var actualResponse account.DriverMyInsuranceResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actualResponse)

		exptResponse := account.DriverMyInsuranceResponse{
			Insurances: []account.DriverInsuranceResponse{
				{
					Type:           "motorcycle",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันรถจักรยานยนต์",
					Subtitle:       "Bangkok Insurance",
					Broker:         model.DriverInsuranceBroker("rabbit"),
					FullName:       "DRV_CHIANG_MAI_2_ONLINE_FIRST_NAME DRV_CHIANG_MAI_2_ONLINE_LAST_NAME",
					Message:        "",
					RegisteredDate: "2025-02-01T07:00:00.000Z",
					StartDate:      "2025-02-14T17:01:00.000Z",
					EndDate:        "2025-03-15T09:30:00.000Z",
					Option: account.DriverInsuranceOptionalResponse{
						Type:         account.DriverInsuranceOptionalType("REJECTED"),
						Title:        "ไม่อนุมัติการต่ออายุกรมธรรม์",
						Subtitle:     "คุณสามารถลงทะเบียนใหม่ได้ในเดือนถัดไป",
						IsCTADisplay: false,
					},
				},
				{
					Type:           "personal_accident",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันอุบัติเหตุ",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "",
					Message:        "ประกันอุบัติเหตุพื้นฐานคุ้มครองทุกการทำงาน ที่ไลน์แมนมอบให้ไรเดอร์ทุกคนโดยไม่มีค่าใช้จ่าย",
					RegisteredDate: "0001-01-01T00:00:00.000Z",
					StartDate:      "0001-01-01T00:00:00.000Z",
					EndDate:        "0001-01-01T00:00:00.000Z",
				},
			},
		}

		assert.Equal(tt, exptResponse, actualResponse)
	})

	t.Run("already have a Rabbit insurance, do nothing until expired", func(tt *testing.T) {
		timeutils.FreezeWithTime(time.Date(2025, 3, 16, 0, 0, 0, 0, timeutil.BangkokLocation()).UnixMilli())
		defer timeutils.Unfreeze()

		gctx := newUpdateDriverStatusHandlerRequest(container.RedisTokenStore, "DRV_CHIANG_MAI_2_ONLINE")

		container.StubGRPCFormService.EXPECT().
			ListForm(gctx.GinCtx(), gomock.Any()).Return(&formServicePb.ListFormResponse{
			Data: []*formServicePb.Form{},
		}, nil)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(tt, http.StatusOK)

		var actualResponse account.DriverMyInsuranceResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actualResponse)

		exptResponse := account.DriverMyInsuranceResponse{
			Insurances: []account.DriverInsuranceResponse{
				{
					Type:           "motorcycle",
					Status:         account.InsuranceStatus("EXPIRED"),
					Title:          "ประกันรถจักรยานยนต์",
					Subtitle:       "Bangkok Insurance",
					Broker:         model.DriverInsuranceBroker("rabbit"),
					FullName:       "DRV_CHIANG_MAI_2_ONLINE_FIRST_NAME DRV_CHIANG_MAI_2_ONLINE_LAST_NAME",
					Message:        "",
					RegisteredDate: "2025-02-01T07:00:00.000Z",
					StartDate:      "2025-02-14T17:01:00.000Z",
					EndDate:        "2025-03-15T09:30:00.000Z",
				},
				{
					Type:           "personal_accident",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันอุบัติเหตุ",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "",
					Message:        "ประกันอุบัติเหตุพื้นฐานคุ้มครองทุกการทำงาน ที่ไลน์แมนมอบให้ไรเดอร์ทุกคนโดยไม่มีค่าใช้จ่าย",
					RegisteredDate: "0001-01-01T00:00:00.000Z",
					StartDate:      "0001-01-01T00:00:00.000Z",
					EndDate:        "0001-01-01T00:00:00.000Z",
				},
			},
		}

		assert.Equal(tt, exptResponse, actualResponse)
	})

	t.Run("already buy Chubb insurance and it's currenly an active time", func(tt *testing.T) {
		timeutils.FreezeWithTime(time.Date(2025, 3, 16, 0, 0, 0, 0, timeutil.BangkokLocation()).UnixMilli())
		defer timeutils.Unfreeze()

		gctx := newUpdateDriverStatusHandlerRequest(container.RedisTokenStore, "GRAB-001")

		formStruct, _ := structpb.NewStruct(map[string]any{
			"type":      "Motor", // ["PA", "Motor", "Motor+PA"]
			"firstName": "GRAB-001_ONLINE_FIRST_NAME",
			"lastName":  "GRAB-001_ONLINE_LAST_NAME",
		})

		container.StubGRPCFormService.EXPECT().
			ListForm(gctx.GinCtx(), gomock.Any()).Return(&formServicePb.ListFormResponse{
			Data: []*formServicePb.Form{
				{
					Id:          "FORM_ID03",
					RefId:       "DRIVER_ID",
					FormType:    formServicePb.FormType_FORM_TYPE_INSURANCE_REGISTRATION,
					FormSubtype: formServicePb.FormSubtype_FORM_SUBTYPE_NONE,
					Status:      formServicePb.FormStatus_FORM_STATUS_APPROVED,
					Value:       formStruct,
					IssuedAt:    timestamppb.New(time.Date(2025, 3, 2, 17, 0, 0, 0, timeutil.BangkokLocation())),
					CreatedAt:   timestamppb.New(time.Date(2025, 3, 2, 18, 0, 0, 0, timeutil.BangkokLocation())),
				},
			},
		}, nil)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(tt, http.StatusOK)

		var actualResponse account.DriverMyInsuranceResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actualResponse)

		exptResponse := account.DriverMyInsuranceResponse{
			Insurances: []account.DriverInsuranceResponse{
				{
					Type:           "motorcycle",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันรถจักรยานยนต์",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "GRAB-001_FIRST_NAME GRAB-001_LAST_NAME",
					Message:        "",
					RegisteredDate: "2025-03-01T07:00:00.000Z",
					StartDate:      "2025-03-14T17:01:00.000Z",
					EndDate:        "2025-04-15T09:30:00.000Z",
				},
				{
					Type:           "personal_accident",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันอุบัติเหตุ",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "",
					Message:        "ประกันอุบัติเหตุพื้นฐานคุ้มครองทุกการทำงาน ที่ไลน์แมนมอบให้ไรเดอร์ทุกคนโดยไม่มีค่าใช้จ่าย",
					RegisteredDate: "0001-01-01T00:00:00.000Z",
					StartDate:      "0001-01-01T00:00:00.000Z",
					EndDate:        "0001-01-01T00:00:00.000Z",
				},
			},
		}

		assert.Equal(tt, exptResponse, actualResponse)
	})

	t.Run("already have a Rabbit insurance, currently open to register, already create a form and the insurance is active", func(tt *testing.T) {
		timeutils.FreezeWithTime(time.Date(2025, 3, 11, 0, 0, 0, 0, timeutil.BangkokLocation()).UnixMilli())
		defer timeutils.Unfreeze()

		gctx := newUpdateDriverStatusHandlerRequest(container.RedisTokenStore, "PANDA-001")

		formStruct, _ := structpb.NewStruct(map[string]any{
			"type":      "Motor", // ["PA", "Motor", "Motor+PA"]
			"firstName": "PANDA-001_FIRST_NAME",
			"lastName":  "PANDA-001_LAST_NAME",
		})

		container.StubGRPCFormService.EXPECT().
			ListForm(gctx.GinCtx(), gomock.Any()).Return(&formServicePb.ListFormResponse{
			Data: []*formServicePb.Form{
				{
					Id:          "FORM_ID03",
					RefId:       "DRIVER_ID",
					FormType:    formServicePb.FormType_FORM_TYPE_INSURANCE_REGISTRATION,
					FormSubtype: formServicePb.FormSubtype_FORM_SUBTYPE_NONE,
					Status:      formServicePb.FormStatus_FORM_STATUS_APPROVED,
					Value:       formStruct,
					IssuedAt:    timestamppb.New(time.Date(2025, 3, 2, 17, 0, 0, 0, timeutil.BangkokLocation())),
					CreatedAt:   timestamppb.New(time.Date(2025, 3, 2, 18, 0, 0, 0, timeutil.BangkokLocation())),
				},
			},
		}, nil)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(tt, http.StatusOK)

		var actualResponse account.DriverMyInsuranceResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actualResponse)

		exptResponse := account.DriverMyInsuranceResponse{
			Insurances: []account.DriverInsuranceResponse{
				{
					Type:           "motorcycle",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันรถจักรยานยนต์",
					Subtitle:       "Bangkok Insurance",
					Broker:         model.DriverInsuranceBroker("rabbit"),
					FullName:       "PANDA-001_FIRST_NAME PANDA-001_LAST_NAME",
					Message:        "",
					RegisteredDate: "2025-02-01T07:00:00.000Z",
					StartDate:      "2025-02-14T17:01:00.000Z",
					EndDate:        "2025-03-15T09:30:00.000Z",
				},
				{
					Type:           "personal_accident",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันอุบัติเหตุ",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "",
					Message:        "ประกันอุบัติเหตุพื้นฐานคุ้มครองทุกการทำงาน ที่ไลน์แมนมอบให้ไรเดอร์ทุกคนโดยไม่มีค่าใช้จ่าย",
					RegisteredDate: "0001-01-01T00:00:00.000Z",
					StartDate:      "0001-01-01T00:00:00.000Z",
					EndDate:        "0001-01-01T00:00:00.000Z",
				},
			},
		}

		assert.Equal(tt, exptResponse, actualResponse)
	})
}

func TestAccountAPI_GetMyInsurance_Configurated(t *testing.T) {
	t.Setenv("DRIVER_INSURANCE_MOTORCYCLE_BROKERS_MAPPER", "chubb:042025")
	container := ittest.NewContainer(t)

	if err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_driver_insurances"); err != nil {
		t.Errorf("unexpected error at InitFixture: %v", err)
	}

	newUpdateDriverStatusHandlerRequest := func(tokenStore auth.TokenStorage, driverID string) *testutil.GinContextWithRecorder {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/account/my-insurances")
		gctx.Authorized(tokenStore, driverID)
		return gctx
	}

	t.Run("already buy Chubb insurance and it's currenly an active time", func(tt *testing.T) {
		timeutils.FreezeWithTime(time.Date(2025, 3, 16, 0, 0, 0, 0, timeutil.BangkokLocation()).UnixMilli())
		defer timeutils.Unfreeze()

		gctx := newUpdateDriverStatusHandlerRequest(container.RedisTokenStore, "GRAB-001")

		formStruct, _ := structpb.NewStruct(map[string]any{
			"type":      "Motor", // ["PA", "Motor", "Motor+PA"]
			"firstName": "GRAB-001_ONLINE_FIRST_NAME",
			"lastName":  "GRAB-001_ONLINE_LAST_NAME",
		})

		container.StubGRPCFormService.EXPECT().
			ListForm(gctx.GinCtx(), gomock.Any()).Return(&formServicePb.ListFormResponse{
			Data: []*formServicePb.Form{
				{
					Id:          "FORM_ID03",
					RefId:       "DRIVER_ID",
					FormType:    formServicePb.FormType_FORM_TYPE_INSURANCE_REGISTRATION,
					FormSubtype: formServicePb.FormSubtype_FORM_SUBTYPE_NONE,
					Status:      formServicePb.FormStatus_FORM_STATUS_APPROVED,
					Value:       formStruct,
					IssuedAt:    timestamppb.New(time.Date(2025, 3, 2, 17, 0, 0, 0, timeutil.BangkokLocation())),
					CreatedAt:   timestamppb.New(time.Date(2025, 3, 2, 18, 0, 0, 0, timeutil.BangkokLocation())),
				},
			},
		}, nil)

		container.GinEngineRouter.HandleContext(gctx.GinCtx())
		gctx.AssertResponseCode(tt, http.StatusOK)

		var actualResponse account.DriverMyInsuranceResponse
		testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actualResponse)

		exptResponse := account.DriverMyInsuranceResponse{
			Insurances: []account.DriverInsuranceResponse{
				{
					Type:           "motorcycle",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันรถจักรยานยนต์",
					Subtitle:       "Bangkok Insurance",
					Broker:         model.DriverInsuranceBroker("rabbit"),
					FullName:       "GRAB-001_FIRST_NAME GRAB-001_LAST_NAME",
					Message:        "",
					RegisteredDate: "2025-03-01T07:00:00.000Z",
					StartDate:      "2025-03-14T17:01:00.000Z",
					EndDate:        "2025-04-15T09:30:00.000Z",
				},
				{
					Type:           "personal_accident",
					Status:         account.InsuranceStatus("ACTIVE"),
					Title:          "ประกันอุบัติเหตุ",
					Subtitle:       "Chubb",
					Broker:         model.DriverInsuranceBroker("chubb"),
					FullName:       "",
					Message:        "ประกันอุบัติเหตุพื้นฐานคุ้มครองทุกการทำงาน ที่ไลน์แมนมอบให้ไรเดอร์ทุกคนโดยไม่มีค่าใช้จ่าย",
					RegisteredDate: "0001-01-01T00:00:00.000Z",
					StartDate:      "0001-01-01T00:00:00.000Z",
					EndDate:        "0001-01-01T00:00:00.000Z",
				},
			},
		}

		assert.Equal(tt, exptResponse, actualResponse)
	})
}
