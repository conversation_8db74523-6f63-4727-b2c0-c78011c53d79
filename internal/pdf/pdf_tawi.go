//go:generate mockgen -source=./pdf.go -destination=./mock_pdf/mock_pdf.go -package=mock_pdf

package pdf

import (
	"fmt"
	"strconv"

	"github.com/signintech/gopdf"
	"golang.org/x/text/message"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func (p PDFBuilderImpl) GenerateTawi50(taxpayer Tawi50TaxPayer, w model.WithholdingTaxCertificate) ([]byte, error) {
	pdf := gopdf.GoPdf{}
	pdf.Start(gopdf.Config{PageSize: *gopdf.PageSizeA4})

	err := p.generateIncomeSummaryPage(&pdf, w, taxpayer)
	if err != nil {
		logx.Error().Err(err).Str("driver_id", w.DriverID).Msg("failed to generate income summary")
		return nil, fmt.Errorf("failed to generate income summary: %w", err)
	}

	err = p.generateTawiPage(&pdf, taxpayer, w)
	if err != nil {
		logx.Error().Err(err).Str("driver_id", w.DriverID).Msg("failed to generate tawi page")
		return nil, fmt.Errorf("failed to generate tawi page: %w", err)
	}

	bytesPdf, er := pdf.GetBytesPdfReturnErr()
	if er != nil {
		logx.Error().Err(er).Str("driver_id", w.DriverID).Msg("failed to get pdf bytes")
		return nil, er
	}
	return bytesPdf, nil
}

func (p PDFBuilderImpl) generateTawiPage(pdf *gopdf.GoPdf, taxpayer Tawi50TaxPayer, w model.WithholdingTaxCertificate) error {
	pdf.AddPage()
	if err := loadFont(pdf, "main", "THSarabunNew.ttf"); err != nil {
		return err
	}
	if err := pdf.SetFont("main", "", 12); err != nil {
		return err
	}

	if id, err := p.pdfGenerator.LoadPDFTemplate(pdf, "tawi-template.pdf"); err != nil {
		return err
	} else {
		p.pdfGenerator.SetA4Resolution(pdf, id)
	}

	printText := func(x, y float64, text string) {
		pdf.SetX(x)
		pdf.SetY(y)
		pdf.Text(text)
	}

	printText(85, 110, taxpayer.TaxpayerName) // lm name
	if err := pdf.SetFont("main", "", 10); err != nil {
		return err
	}
	printText(90, 135, taxpayer.TaxpayerAddress) // lm address
	if err := pdf.SetFont("main", "", 12); err != nil {
		return err
	}

	if len(taxpayer.TaxIdentificationNo) >= 13 {
		for i, x := range []float64{371, 388, 399, 410, 420, 437, 449, 459, 470, 481, 499, 509, 527} {
			printText(x, 94, taxpayer.TaxIdentificationNo[i]) // lm tax id
		}
	}

	name := fmt.Sprintf("%s %s", w.DriverProfile.Firstname, w.DriverProfile.Lastname)
	printText(85, 185, name) // driver name

	address := fmt.Sprintf("%s %s %s %s %s %s",
		getMooString(w.DriverProfile.Address.Moo.String()),
		w.DriverProfile.Address.HouseNumber.String(),
		w.DriverProfile.Address.Subdistrict.String(),
		w.DriverProfile.Address.District.String(),
		w.DriverProfile.Address.Province.String(),
		w.DriverProfile.Address.Zipcode.String(),
	)
	printText(90, 214, address) // driver address

	citizenID := w.DriverProfile.CitizenID.String()
	if len(citizenID) >= 13 {
		for i, x := range []float64{371, 388, 399, 410, 420, 437, 449, 459, 470, 481, 499, 509, 527} {
			printText(x, 164, string(citizenID[i])) // driver citizen id
		}
	}

	i, err := strconv.Atoi(w.Year)
	if err != nil {
		i = taxpayer.IssuingYear
	}
	printText(353, 662, strconv.Itoa(i))                         // tax year
	printText(170, 662, "การขนส่งหรือรับจ้างด้วยยานพาหนะ 40(8)") // fixed text

	printText(335, 795, strconv.Itoa(taxpayer.IssuingDate)) // issuing date
	printText(380, 795, timeutil.ThaiMonth(taxpayer.IssuingMonth))
	printText(430, 795, strconv.Itoa(taxpayer.IssuingYear))

	np := message.NewPrinter(message.MatchLanguage("en"))
	printText(415, 662, np.Sprintf("%.2f", w.AmountPaid))  // revenue amount
	printText(490, 662, np.Sprintf("%.2f", w.TaxWithheld)) // wht amount
	printText(415, 685, np.Sprintf("%.2f", w.AmountPaid))  // sum revenue amount
	printText(490, 685, np.Sprintf("%.2f", w.TaxWithheld)) // sum wht amount

	printText(210, 707, types.NewMoney(w.TaxWithheld).GetBahtText()) // tax thai chars

	pdf.SetFontSize(24)
	pdf.RotateReset()
	pdf.Rotate(-25, 210, 238)
	printText(436, 137, "/") // check mark
	pdf.RotateReset()
	pdf.Rotate(-25, 84, 722)
	printText(110, 738, "/")
	return nil
}

func (p PDFBuilderImpl) generateIncomeSummaryPage(pdf *gopdf.GoPdf, w model.WithholdingTaxCertificate, taxpayer Tawi50TaxPayer) error {
	pdf.AddPage()
	if err := loadFont(pdf, "main", "THSarabunNewBold.ttf"); err != nil {
		return err
	}
	if err := pdf.SetFont("main", "normal", 15); err != nil {
		return err
	}

	if id, err := p.pdfGenerator.LoadPDFTemplate(pdf, "income-summary-template.pdf"); err != nil {
		return err
	} else {
		p.pdfGenerator.SetA4Resolution(pdf, id)
	}

	printText := func(x, y float64, text string) {
		pdf.SetX(x)
		pdf.SetY(y)
		pdf.Text(text)
	}

	// name
	name := fmt.Sprintf("%s %s", w.DriverProfile.Firstname, w.DriverProfile.Lastname)
	printText(70, 235, name)

	// citizen ID
	citizenID := w.DriverProfile.CitizenID.String()
	x := 167.0
	for _, char := range citizenID {
		printText(x, 255, string(char))
		x += 5.8
	}

	// income summary
	np := message.NewPrinter(message.MatchLanguage("en"))
	printText(347, 387, np.Sprintf("%.2f", w.IncomeSummary.SumOfTaxBase))

	printText(347, 425, np.Sprintf("%.2f", w.IncomeSummary.SumOfAgentIncome))
	printText(347, 468, np.Sprintf("%.2f", w.IncomeSummary.SumOfTaxBaseAndSumOfAgentIncome))
	printText(417, 387, np.Sprintf("%.2f", w.IncomeSummary.SumOfWithHoldingTax))
	printText(417, 467, np.Sprintf("%.2f", w.IncomeSummary.SumOfWithHoldingTax))

	printText(475, 618, strconv.Itoa(taxpayer.IssuingDate)) // issuing date
	printText(490, 618, timeutil.ThaiMonth(taxpayer.IssuingMonth))
	printText(511, 618, strconv.Itoa(taxpayer.IssuingYear))

	return nil
}
