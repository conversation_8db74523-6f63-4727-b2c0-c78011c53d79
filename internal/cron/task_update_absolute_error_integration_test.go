//go:build integration_test
// +build integration_test

package cron_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
)

func TestProvideAbsoluteErrorTask(t *testing.T) {
	t.Run("should update service areas successfully", func(tt *testing.T) {
		ctn := ittest.NewContainer(t)
		result, err := ctn.UpdateAbsoluteErrorStatsTask.Execute(context.Background())
		require.NoError(t, err)

		area, err := ctn.MongoServiceAreaRepository.GetByRegion(context.Background(), "BKK")
		require.NoError(t, err)
		require.Equal(t, 0, len(result.Fail))
		require.Equal(t, &model.Duration{
			Duration: 668 * time.Second,
		}, area.Distribution.CompleteTimeCalculation.AbsoluteError)
		require.Equal(t, &model.Duration{
			Duration: 352 * time.Second,
		}, area.Distribution.CompleteTimeCalculation.MeanAbsoluteError)
	})
}
