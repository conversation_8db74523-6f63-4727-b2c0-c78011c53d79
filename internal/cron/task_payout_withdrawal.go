//go:generate mockgen -source=./task_tawi50_generator.go -destination=./mock_task_tawi50_generator.go -package=cron

package cron

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/absinthe/metrics/push"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/email"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type PayoutWithdrawalTask struct {
	txnRepo            repository.TransactionRepository
	paymentService     payment.PaymentService
	emailService       email.EmailService
	cfg                config.PaymentConfig
	mPusher            push.MetricPusher
	totalMetric        push.GaugeMetricPush
	featureflagService featureflag.Service
}

func ProvidePayoutWithdrawalTask(txnRepo repository.TransactionRepository, paymentService payment.PaymentService, emailService email.EmailService, cfg config.PaymentConfig, featureflagService featureflag.Service) *PayoutWithdrawalTask {
	mPusher := push.NewDefaultMetricPusher("driver-payout-withdrawal-cron", "driver_payout_withdrawal_cron")
	totalMetric := push.NewGaugeMetricPush(
		mPusher,
		"payout_withdrawal_count",
		"number of payout withdrawal processing",
		"state")

	return &PayoutWithdrawalTask{
		txnRepo:            txnRepo,
		paymentService:     paymentService,
		emailService:       emailService,
		cfg:                cfg,
		mPusher:            mPusher,
		totalMetric:        totalMetric,
		featureflagService: featureflagService,
	}
}

func (pw *PayoutWithdrawalTask) Execute(ctx context.Context, _ ...interface{}) (Result, error) {
	isUOBPayoutEnabled := pw.isUOBPayoutEnabled(ctx)
	result := NewResult()
	if !isUOBPayoutEnabled {
		logx.Info().Msg("Auto Payout Withdrawal Disabled")
		return *result, nil
	}
	defer pw.mPusher.Add(ctx)

	autoApprovalEnabled := pw.featureflagService.IsEnabledWithDefaultTrue(ctx, featureflag.IsUOBPayoutAutoApprovalTransactionEnabled.Name)
	if autoApprovalEnabled {
		now := timeutil.BangkokNow().Round(60 * time.Second)
		start := now.Add(pw.cfg.StartDelayTime)
		end := now.Add(pw.cfg.EndDelayTime)
		logx.Info().Msgf("Payout Withdrawal. Approve Pending Fraud Transaction, Start:%v , End %v  ", start.Format(time.DateTime), end.Format(time.DateTime))
		err := pw.txnRepo.ApprovePendingFraudTransaction(ctx, pw.cfg.RequestedBy, start, end)
		if err != nil {
			pw.MetricObserve(ctx, result, err)
			log := fmt.Sprintf("Payout Withdrawal. Approve Pending Fraud Transaction, Start:%v , End %v  Err: %v", start.Format(time.DateTime), end.Format(time.DateTime), err)
			logx.Warn().Msg(log)
			return *result, err
		}
	} else {
		logx.Info().Msgf("Auto Approval Transaction Disabled")
	}

	request := payment.ListTransactionReq{RequestedBy: pw.cfg.RequestedBy}
	resultRes, err := pw.paymentService.DoUobBulkProcessWithdraw(context.Background(), request)
	if err != nil {
		pw.MetricObserve(ctx, result, err)
		tos, from, password := pw.cfg.EmailTransactionTos, pw.cfg.EmailTransactionFrom, pw.cfg.EmailTransactionPassword
		subject := fmt.Sprintf("UOB Rider withdrawal Result [%s]", time.Now().Format(time.DateTime))
		emailBody := fmt.Sprintf("UOB Rider unsuccessful withdrawal. For more information please contact engineering team.")
		if err := pw.emailService.Send(subject, emailBody, tos, from, password, []email.File{}); err != nil {
			logrus.Errorf("[UobBulkProcessWithdraw] Send mail error: %v", err)
		}
	} else {

		for _, success := range resultRes.Successes {
			result.AddSuccess(success.WithdrawRefID)
		}

		for _, fail := range resultRes.Failures {
			result.AddFail(fail.WithdrawRefID)
		}

		log := fmt.Sprintf("Auto Payout Withdrawal Success")
		logx.Info().Msg(log)
	}

	pw.MetricObserve(ctx, result, nil)
	return *result, nil
}

func (pw *PayoutWithdrawalTask) isUOBPayoutEnabled(ctx context.Context) bool {
	if !pw.cfg.AutoPayoutEnabled {
		logx.Info().Msg("ENV Config Auto Payout Withdrawal Disabled")
		return false
	}
	ctx, cancel := context.WithTimeout(ctx, 180*time.Second)
	defer cancel()

	enabled := pw.featureflagService.IsEnabledWithDefaultTrue(ctx, featureflag.IsUOBPayoutEnabled.Name)
	if ctx.Err() != nil {
		logx.Error().Msgf("Timeout checking UOB payout feature flag. Return True")
		return true
	}
	logx.Info().Msgf("PayoutWithdrawalTask IsUOBPayoutEnabled : %v", enabled)
	return enabled
}

func (pw *PayoutWithdrawalTask) Name() string {
	return "PayoutWithdrawal"
}

func (pw *PayoutWithdrawalTask) MetricObserve(ctx context.Context, result *Result, err error) {
	pw.totalMetric.Observe(ctx, float64(len(result.Success)), push.NewAttribute("state", "success"))
	pw.totalMetric.Observe(ctx, float64(len(result.Fail)), push.NewAttribute("state", "fail"))

	if err != nil {
		pw.totalMetric.Observe(ctx, 1, push.NewAttribute("state", "error"))
	} else {
		pw.totalMetric.Observe(ctx, 0, push.NewAttribute("state", "error"))
	}
}
