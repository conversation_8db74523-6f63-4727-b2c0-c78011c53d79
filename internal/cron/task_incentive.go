package cron

import (
	"context"
	"encoding"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"github.com/twpayne/go-geom"
	"github.com/twpayne/go-geom/xy"
	"gopkg.in/guregu/null.v4"

	"git.wndv.co/go/logx/v2"
	absintheUtils "git.wndv.co/lineman/absinthe/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/mathutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

type IncentiveTask struct {
	orderService          repository.OrderRepository
	driverTransService    payment.DriverTransactionService
	incentiveRepo         incentive.IncentiveRepository
	driverRepository      repository.DriverRepository
	OrderInfoRepository   repository.DriverOrderInfoRepository
	serviceAreaRepository repository.ServiceAreaRepository
	cfg                   IncentiveConfig
	batchID               string
}

type jacketBox struct {
	jacket bool
	box    bool
}

func ProvideIncentiveTask(ordSvc repository.OrderRepository, drivTransSvc payment.DriverTransactionService, inRepo incentive.IncentiveRepository,
	driverRepository repository.DriverRepository, serviceAreaRepository repository.ServiceAreaRepository, cfg IncentiveConfig, OrderInfo repository.DriverOrderInfoRepository,
) *IncentiveTask {
	return &IncentiveTask{
		orderService:          ordSvc,
		driverTransService:    drivTransSvc,
		incentiveRepo:         inRepo,
		driverRepository:      driverRepository,
		serviceAreaRepository: serviceAreaRepository,
		cfg:                   cfg,
		OrderInfoRepository:   OrderInfo,
	}
}

func (it *IncentiveTask) Name() string {
	return "Incentive"
}

func (it *IncentiveTask) Execute(ctx context.Context, params ...interface{}) (Result, error) {
	if len(params) <= 0 {
		return Result{}, ErrInvalidParam
	}
	dateStr, ok := params[0].(string)
	if !ok {
		return Result{}, ErrInvalidParam
	}

	now := timeutil.GetTimeFromContext(ctx)
	targetDate, err := timeutil.ParseStringToDate(dateStr, now)
	logrus.Infof("Target date is %v", targetDate)
	if err != nil {
		return Result{}, err
	}

	result := NewResult()
	it.batchID = utils.GenerateUUID()
	logrus.Infof("batchID %s", it.batchID)

	res, err := it.processIncentives(ctx, targetDate)
	if err != nil {
		return Result{}, err
	}

	result.AddSuccess(res.Success...)
	result.AddFail(res.Fail...)
	result.AddProceeds(res.Proceeds...)

	return *result, nil
}

func (it *IncentiveTask) processIncentives(ctx context.Context, targetDate time.Time) (Result, error) {
	targetDateUTC := timeutil.DateTruncate(targetDate)
	targetDateUTC = targetDateUTC.In(time.UTC)

	logrus.Infof("GetActiveIncentiveByTime is date range start lte %v", targetDateUTC)

	inc, err := it.incentiveRepo.GetActiveIncentiveByTime(ctx, targetDateUTC)
	if err != nil {
		return Result{}, err
	}

	logrus.Infof("Total amount incentive settings is: %v", len(inc))
	result := NewResult()
	for _, in := range inc {
		start := in.DateRange.Start
		end := in.DateRange.End

		switch in.PaymentType {
		case incentive.Daily:
			if !timeutil.IsBetweenEqual(targetDateUTC, start, end) {
				continue
			}
			s := targetDateUTC
			e := timeutil.DateCeiling(targetDate).In(time.UTC)
			logrus.Infof("Process incentive id %v name %v with date range start=%v, end=%v and paymentType=%v",
				in.IncentiveID, in.Name, s, e, in.PaymentType)

			res, err := it.computeByIncentive(ctx, in, s, e)
			if err != nil {
				continue
			}
			result.AddSuccess(res.Success...)
			result.AddFail(res.Fail...)
			result.AddProceeds(res.Proceeds...)
		case incentive.Period, incentive.PeriodStreak:
			if !isSameDay(targetDate, end) {
				continue
			}
			s := in.DateRange.Start
			e := in.DateRange.End

			logrus.Infof("Process incentive id %v name %v with date range start=%v, end=%v and paymentType=%v",
				in.IncentiveID, in.Name, in.DateRange.Start, in.DateRange.End, in.PaymentType)

			res, err := it.computeByIncentive(ctx, in, s, e)
			if err != nil {
				continue
			}
			result.AddSuccess(res.Success...)
			result.AddFail(res.Fail...)
			result.AddProceeds(res.Proceeds...)
		}
	}

	return *result, nil
}

func (it *IncentiveTask) computeByIncentive(ctx context.Context, inc incentive.Incentive, start time.Time, end time.Time) (Result, error) {
	batchSize := it.cfg.BatchSize
	logrus.Infof("Process incentive in region %v, batch %d", inc.Region, batchSize)

	result, errs := NewResult(), types.NewErrors()

	var lastID string

	findQuery := persistence.BuildDriverQuery()
	findQuery.WithRegion(inc.Region.String())
	if inc.Rating != nil {
		r := null.FloatFromPtr(inc.Rating)
		findQuery.WithGTESMARating(r)
	}

	if inc.WhitelistIDs != nil && len(inc.WhitelistIDs) != 0 {
		findQuery.WithDriverIDs(inc.WhitelistIDs)
	}

	if inc.BlacklistIDs != nil && len(inc.BlacklistIDs) != 0 {
		findQuery.WithoutDriverIDs(inc.BlacklistIDs)
	}

	for {

		if lastID != "" {
			findQuery.WithGTDriverID(lastID)
		}

		drivers, err := it.driverRepository.FindWithQueryAndSort(ctx, findQuery, []string{"driver_id"}, 0, batchSize)
		if err != nil {
			return *result, err
		}

		// important check make loop exit
		if len(drivers) == 0 {
			return *result, nil
		}

		lastID = drivers[len(drivers)-1].DriverID

		logrus.Infof("last driverID %s", lastID)

		driverIDs, doiMap, err := it.filterDriverIDs(ctx, inc, drivers, start, end)
		if err != nil {
			logrus.Errorf("filterDriverIDs err: %v", err)
			continue
		}

		logrus.Infof("drivers amount: %v,  pass incentive validation amount %v", len(drivers), len(driverIDs))

		driverSize := len(driverIDs)
		if driverSize == 0 {
			continue
		}

		result.AddProceeds(driverIDs...)
		var serviceTypes []string
		for _, st := range inc.ServiceTypes {
			serviceTypes = append(serviceTypes, st.String())
		}

		logrus.Infof("Find order in complete time between start=%v end=%v in service types %v", start, end, serviceTypes)

		it.processDriverOrder(ctx, &inc, start, end, driverIDs, serviceTypes, result, doiMap, errs)
	}
}

func (it *IncentiveTask) processDriverOrder(ctx context.Context, inc *incentive.Incentive, start, end time.Time, driverIDs, serviceTypes []string,
	result *Result, doiMap map[string]model.DriverOrderInfo, errs types.Errors,
) {
	driverOrdersMap := make(map[string][]model.Order)
	orders, err := it.orderService.FindOrderCompletedByTimeAndDriver(ctx, start, end, driverIDs, serviceTypes, 0, 0)
	if err != nil {
		_ = errs.AddError(errors.WithMessagef(err, "cannot list order"))
		result.AddFail(driverIDs...)
		return
	}

	for _, ord := range orders {
		isValidOrderShiftType := validOrderShiftType(ord, inc.OrderShiftType)
		if !isValidOrderShiftType {
			logx.Info().
				Str("task_id", it.Name()).
				Msgf("order id [%s] of driver id [%s] filtered out: invalid order shift type [%s]", ord.OrderID, ord.Driver, string(inc.OrderShiftType))
		}

		isValidIncentiveTime := validIncentiveTime(ord.History, inc.Times, ord.CreatedAt)
		if !isValidIncentiveTime {
			logx.Info().
				Str("task_id", it.Name()).
				Str("incentive_criteria_times", fmt.Sprint(inc.Times)).
				Msgf("order id [%s] of driver id [%s] filtered out: invalid incentive time", ord.OrderID, ord.Driver)
		}
		isValidCoordinates := validCoordinates(inc, &ord)
		if !isValidCoordinates {
			logx.Info().
				Str("task_id", it.Name()).
				Msgf("order id [%s] of driver id [%s] filtered out: invalid coordinates", ord.OrderID, ord.Driver)
		}

		isVoidedWage := ord.IsVoidedWage
		if isVoidedWage {
			logx.Info().
				Str("task_id", it.Name()).
				Msgf("order id [%s] of driver id [%s] filtered out: is voided wage", ord.OrderID, ord.Driver)
		}

		if isValidOrderShiftType && isValidIncentiveTime && isValidCoordinates && !isVoidedWage {
			driverOrder, ok := driverOrdersMap[ord.Driver]
			if !ok {
				driverOrder = make([]model.Order, 0, 50)
			}

			driverOrder = append(driverOrder, ord)
			driverOrdersMap[ord.Driver] = driverOrder
		}
	}

	it.processTransaction(ctx, inc, end, driverOrdersMap, result, doiMap, errs)
}

func (it *IncentiveTask) processTransaction(ctx context.Context, inc *incentive.Incentive, incentiveLastDay time.Time, driverOrdersMap map[string][]model.Order,
	result *Result, doiMap map[string]model.DriverOrderInfo, errs types.Errors,
) {
	for driverID, ords := range driverOrdersMap {
		ordAmount := len(ords)
		amount := inc.CalculatePrice(int64(ordAmount))

		if inc.PaymentType == incentive.PeriodStreak {
			doi := doiMap[driverID]
			p := incentive.NewRealOrderCountProvider(ords)
			amount = inc.CalculateStreakPrice(p, &doi)
		}

		if amount <= 0 {
			result.AddSuccess(driverID)
			continue
		}

		incentivePaymentDate := timeutils.StartOfTheDayInBangkok(incentiveLastDay).AddDate(0, 0, 1).Format(time.DateOnly)
		isIncentivePaid, err := it.driverTransService.IsIncentivePaid(ctx, inc.IncentiveID, incentivePaymentDate, driverID)
		if err != nil {
			_ = errs.AddError(errors.WithMessagef(err, "fail to get incentive transaction : %s", driverID))
			result.AddFail(driverID)
			continue
		}
		if isIncentivePaid {
			result.AddSuccess(driverID)
			logrus.Infof("Driver %s have %v order's and already got %v in incentive ID %v", driverID, ordAmount, amount, inc.IncentiveID)
			continue
		}
		logrus.Infof("Driver %s have %v order's and get incentive is %v in incentive ID %v", driverID, ordAmount, amount, inc.IncentiveID)

		refIDs := make([]string, ordAmount)
		for i, size := 0, ordAmount; i < size; i++ {
			refIDs[i] = ords[i].OrderID
		}

		taxRefID := utils.GenerateUUID()
		info := model.NewIncentiveTransactionInfo(driverID, amount, refIDs)
		info.TaxRefID = taxRefID
		info.IncentiveNames = []string{inc.Name}
		info.IncentiveID = inc.IncentiveID
		info.IncentiveBatchID = it.batchID

		info.IncentiveDate = timeutil.BangkokNow().Format(time.DateOnly) // YYYY-MM-DD
		info.IncentivePaymentDate = incentivePaymentDate

		if len(inc.Sources) != 0 {
			info.IncentiveSources = inc.Sources
		}

		taxAmount := mathutil.RoundFloat64(info.Amount.Float64() * it.cfg.WithHoldingTax)
		taxInfo := model.NewWithholdingCreditTransactionInfo(info.DriverID, info.OrderID, info.TripID, types.Money(taxAmount))
		taxInfo.TaxRefID = taxRefID
		transInfos := []model.TransactionInfo{*info, *taxInfo}

		isFullTimeDriver := isFullTimeDriver(ords)
		driverType, driverVendorID := getDriverTypeAndVendorID(ords)

		if _, _, err = it.driverTransService.ProcessDriverTransaction(ctx,
			driverID,
			model.SystemTransactionChannel,
			model.IncentiveTransactionAction,
			model.SuccessTransactionStatus,
			service.TransactionInfos(transInfos...),
			service.WithTransactionOptions(
				model.WithNewTransactionRefID(),
				model.WithApplyTipTransaction(),
				model.WithApplyDriverType(driverType),
				model.WithApplyDriverVendorID(driverVendorID),
			),
			service.WithFullTimeDriverFlag(isFullTimeDriver),
		); err != nil {
			_ = errs.AddError(errors.WithMessagef(err, "fail to add incentive driver : %s", driverID))
			result.AddFail(driverID)
		} else {
			result.AddSuccess(driverID)
		}
	}
}

// getDriverIDs get only valid driver with incentive validation
func (it *IncentiveTask) filterDriverIDs(ctx context.Context, inc incentive.Incentive, drivers []model.Driver, start, end time.Time) ([]string, map[string]model.DriverOrderInfo, error) {
	res := types.NewStringSet()
	startForBoxJacketAndTier := start.Add(23 * time.Hour)
	for _, driver := range drivers {
		jacket := driver.GetHaveJacketByTime(startForBoxJacketAndTier)
		tier := driver.GetDriverTierByTime(startForBoxJacketAndTier)
		box := driver.GetHaveBoxByTime(startForBoxJacketAndTier)
		jb := jacketBox{box: box, jacket: jacket}

		isValidTier := validTier(&inc, tier)
		if !isValidTier {
			logx.Info().
				Str("task_id", it.Name()).
				Str("incentive_tier", strings.Join(inc.Tiers, ",")).
				Msgf("driver id [%s] filtered out: invalid tier [%s]", driver.DriverID, tier.ToString())
		}
		isValidJacketAndBox := validJacketAndBox(&inc, jb)
		if !isValidJacketAndBox {
			logx.Info().
				Str("task_id", it.Name()).
				Str("incentive_criteria_jacket", encodeText(null.BoolFromPtr(inc.Jacket))).
				Str("incentive_criteria_box", encodeText(null.BoolFromPtr(inc.Box))).
				Msgf("driver id [%s] filtered out: invalid jacket and box [jacket=%t,box=%t]", driver.DriverID, jb.jacket, jb.box)
		}

		if isValidTier && isValidJacketAndBox {
			res.Add(driver.DriverID)
		}
	}

	doiMap := make(map[string]model.DriverOrderInfo, len(drivers))
	if inc.AR != nil || inc.CR != nil {
		// for reduce hit database.
		driverIDs := res.GetElements()
		drivOrderInfo, err := it.OrderInfoRepository.GetDailyCountsMultipleDrivers(ctx, driverIDs, start, end)
		if err != nil {
			logrus.Errorf("getDriverIDs OrderInfoRepository.GetDailyCountsMultipleDrivers err: %v", err)
			return nil, nil, err
		}
		for _, v := range drivOrderInfo {
			doiMap[v.DriverID] = v
			if inc.PaymentType == incentive.PeriodStreak {
				passed := inc.ValidateStreakARCR(&v, start, end)
				if !passed {
					res.Remove(v.DriverID)
					logx.Info().
						Str("task_id", it.Name()).
						Str("incentive_criteria_ar", encodeText(null.FloatFromPtr(inc.AR))).
						Str("incentive_criteria_cr", encodeText(null.FloatFromPtr(inc.CR))).
						Msgf("driver id [%s] filtered out: insufficient ar/cr", v.DriverID)
				}
				continue
			}

			_, cr, _ := v.GetCR(start, end)
			_, ar, _ := v.GetAR(start, end)

			if !inc.ValidateAR(ar) {
				res.Remove(v.DriverID)
				logx.Info().
					Str("task_id", it.Name()).
					Str("incentive_criteria_ar", encodeText(null.FloatFromPtr(inc.AR))).
					Msgf("driver id [%s] filtered out: invalid ar [%f]", v.DriverID, ar)
			}
			if !inc.ValidateCR(cr) {
				res.Remove(v.DriverID)
				logx.Info().
					Str("task_id", it.Name()).
					Str("incentive_criteria_cr", encodeText(null.FloatFromPtr(inc.CR))).
					Msgf("driver id [%s] filtered out: invalid cr [%f]", v.DriverID, cr)
			}
		}
	}

	return res.GetElements(), doiMap, nil
}

func validTier(inc *incentive.Incentive, tier model.DriverTier) bool {
	// ignore tier
	if len(inc.Tiers) == 0 {
		return true
	}

	return absintheUtils.StrContains(string(tier), inc.Tiers)
}

func validJacketAndBox(inc *incentive.Incentive, info jacketBox) bool {
	// true only, false || nil ignore
	if null.BoolFromPtr(inc.Jacket).Bool {
		if !info.jacket {
			return false
		}
	}

	// true only, false || nil ignore
	if null.BoolFromPtr(inc.Box).Bool {
		if !info.box { // have box only
			return false
		}
	}

	return true
}

func validIncentiveTime(orderTime map[string]time.Time, incTimes []incentive.Times, createdAt time.Time) bool {
	_, ok := orderTime[string(model.StatusCompleted)]
	if !ok {
		return false
	}

	ordTime := timeutil.ToThaiTimeZone(createdAt)
	for _, t := range incTimes {
		startHour, startMinute, startSecond := getTimeHourFromString(t.Start)
		endHour, endMinute, endSecond := getTimeHourFromString(t.End)

		createdHour, createdMinute, createdSecond := ordTime.Hour(), ordTime.Minute(), ordTime.Second()

		inStartTime := createdHour > startHour || (createdHour == startHour && createdMinute > startMinute) ||
			(createdHour == startHour && createdMinute >= startMinute && createdSecond >= startSecond)
		inEndTime := createdHour < endHour || (createdHour == endHour && createdMinute < endMinute) ||
			(createdHour == endHour && createdMinute <= endMinute && createdSecond <= endSecond)

		if inStartTime && inEndTime {
			return true
		}
	}

	return false
}

func getTimeHourFromString(t string) (int, int, int) {
	str := strings.Split(t, ":")
	h, err := strconv.Atoi(str[0])
	if err != nil {
		return 0, 0, 0
	}
	m, err := strconv.Atoi(str[1])
	if err != nil {
		return h, 0, 0
	}
	var s int
	if len(str) == 3 {
		sc, err := strconv.Atoi(str[2])
		if err != nil {
			return h, m, 0
		}
		s = sc
	}
	return h, m, s
}

/* isSameDay first is  Thai time, second is time ceiling and convert to UTC time
   because time in second param should be 16:59:59 (convert to Thai time is 23:59:59)
   ex. cron run 2022-02-07 01:05:00 then first = 2022-02-06 01:05:00 and second = 2022-02-06 16:59:59. it's return true
   ex. cron run 2022-02-07 23:05:00 then first = 2022-02-06 23:05:00 and second = 2022-02-06 16:59:59. it's return true
*/

func isSameDay(first, second time.Time) bool {
	return first.Day() == second.Day() &&
		first.Month() == second.Month() &&
		first.Year() == second.Year()
}

func validCoordinates(inc *incentive.Incentive, ord *model.Order) bool {
	if len(ord.Routes) == 0 {
		return false
	}

	lng := ord.Routes[0].Location.Lng
	lat := ord.Routes[0].Location.Lat

	multiPolygon, err := geom.NewMultiPolygon(geom.XY).SetCoords(inc.GetEffectiveGeometry().Coordinates)
	if err != nil {
		logrus.Warnf("geom new multipolygon SetCoords err: %v", err.Error())
		return false
	}

	point, err := geom.NewPoint(geom.XY).SetCoords(geom.Coord{lng, lat})
	if err != nil {
		logrus.Warnf("geom new point SetCoords err: %v", err.Error())
		return false
	}

	for i := 0; i < multiPolygon.NumPolygons(); i++ {
		if polygonContainsPoint(multiPolygon.Polygon(i), point) {
			return true
		}
	}
	return false
}

func polygonContainsPoint(polygon *geom.Polygon, p *geom.Point) bool {
	exterior := polygon.LinearRing(0)
	if !xy.IsPointInRing(geom.XY, p.Coords(), exterior.FlatCoords()) {
		return false
	}

	num := polygon.NumLinearRings()
	for i := 1; i < num; i++ {
		interior := polygon.LinearRing(i)
		if xy.IsPointInRing(geom.XY, p.Coords(), interior.FlatCoords()) {
			return false
		}
	}

	return true
}

func validOrderShiftType(srcOrder model.Order, targetOrderType incentive.OrderShiftType) bool {
	switch targetOrderType {
	case incentive.AllOrderType:
		return true
	case incentive.OrderShiftType(""):
		return true
	case incentive.OnlyShiftType:
		return len(srcOrder.Shifts) > 0 &&
			len(srcOrder.Routes) >= 2 &&
			srcOrder.Routes[1].PriceSummary.DeliveryFee.ShiftPriceValue > 0
	case incentive.OnlyNonShiftType:
		return len(srcOrder.Shifts) == 0 || (len(srcOrder.Routes) >= 2 &&
			srcOrder.Routes[1].PriceSummary.DeliveryFee.ShiftPriceValue == 0)
	}
	logrus.Warnf("unexpected incentive order type: %v", targetOrderType)
	return true
}

func encodeText(e encoding.TextMarshaler) string {
	b, _ := e.MarshalText()
	if len(b) == 0 {
		return "nil"
	}
	return string(b)
}

func isFullTimeDriver(ords []model.Order) bool {
	for _, ord := range ords {
		if ord.IsFullTimeDriver() {
			return true
		}
	}
	return false
}

func getDriverTypeAndVendorID(ords []model.Order) (string, string) {
	for _, ord := range ords {
		driverType := ord.DriverType.ToString()
		if driverType != "" {
			return driverType, ord.DriverVendorID
		}
	}
	return "", ""
}
