package cron

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type ATTRLogArchiveTask struct {
	dr   repository.DriverRepository
	doi  repository.DriverOrderInfoRepository
	s    repository.ShiftRepository
	traH transaction.TxnHelper
	ath  repository.AttendanceLogHistoryRepository
	cfg  ATTRLogArchiveConfig
}

type attrLogArchiveTaskStats struct {
	totalDriversProceeded               int
	totalArchivedDriversProceeded       int
	totalExpiredDriversProceeded        int
	totalAttendanceLogProceeded         int
	totalArchivedAttendanceLogProceeded int
	totalExpiredAttendanceLogProceeded  int
}

func ProvideATTRLogArchiveTask(dr repository.DriverRepository, doi repository.DriverOrderInfoRepository, s repository.ShiftRepository, traH transaction.TxnHelper, ath repository.AttendanceLogHistoryRepository, cfg ATTRLogArchiveConfig) *ATTRLogArchiveTask {
	return &ATTRLogArchiveTask{dr: dr, doi: doi, s: s, traH: traH, ath: ath, cfg: cfg}
}

func (t *ATTRLogArchiveTask) Name() string {
	return "ATTRLogArchive"
}

func (t *ATTRLogArchiveTask) Execute(ctx context.Context, params ...interface{}) (Result, error) {
	logrus.WithFields(logrus.Fields{
		"params": params,
	}).Info("attr log job started")

	if len(params) <= 0 {
		return Result{}, ErrInvalidParam
	}
	dateStr, ok := params[0].(string)
	if !ok {
		return Result{}, ErrInvalidParam
	}

	runningDate, err := t.parseDate(dateStr)
	if err != nil {
		return Result{}, err
	}

	result := NewResult()
	stats := attrLogArchiveTaskStats{}

	// from - DDMMYYYY 00:00:00
	// to - DDMMYYYY 23:59:59
	from, to := timeutil.DateTruncate(runningDate), timeutil.DateCeiling(runningDate)

	logrus.WithFields(logrus.Fields{
		"params": params,
		"from":   from,
		"to":     to,
	}).Info("with parameter")

	// (1) List all drivers who has attendance logs in between `from` and `to`
	drivers, err := t.dr.FindWhoHasAttendanceLogInBetween(ctx, from, to, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"from": from,
			"to":   to,
		}).Errorf("find attendance log before error: %v", err)
		return Result{}, err
	}

	if len(drivers) == 0 {
		logrus.WithFields(logrus.Fields{
			"params": params,
			"from":   from,
			"to":     to,
		}).Info("no drivers found")
	}

	// (2) Iterate all drivers
	for _, driver := range drivers {
		stats.totalAttendanceLogProceeded += len(driver.AttendanceLogs)
		result.AddProceeds(driver.DriverID)

		// (2.1) List all Shift which belong to this driver and creation time is between `from` and `to`
		q := repository.ShiftQuery{DriverIDs: []string{driver.DriverID}, StartDate: from, EndDate: to}
		shifts, _, err := t.s.Find(ctx, q, 0, 0, repository.WithReadSecondaryPreferred)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"driverID": driver.DriverID,
				"from":     from,
				"to":       to,
			}).Errorf("find shift driver error: %v", err)
			result.AddFail(driver.DriverID)
			continue
		}

		// (2.2) If this driver has attendance logs but couldn't find his/her shift on date, no benefit to keep log going
		// Then expired driver's attendance logs on date.
		if len(shifts) == 0 {
			stats.totalExpiredDriversProceeded += 1
			stats.totalExpiredAttendanceLogProceeded += len(driver.AttendanceLogs)

			err := t.dr.RemoveAttendanceLogInBetween(ctx, driver.DriverID, from, to)
			if err != nil {
				logrus.WithError(err).WithFields(logrus.Fields{
					"driverID": driver.DriverID,
					"from":     from,
					"to":       to,
				}).Errorf("can't expired attendance log on this driver: %v", err)
				result.AddFail(driver.DriverID)
				continue
			}
			logrus.WithFields(logrus.Fields{
				"driverID": driver.DriverID,
				"from":     from,
				"to":       to,
			}).Info("expired driver's attendance log because no shift belong to this driver")
			result.AddSuccess(driver.DriverID)
			continue
		}

		logrus.WithFields(logrus.Fields{
			"driverID":       driver.DriverID,
			"attendanceLogs": len(driver.AttendanceLogs),
			"shifts":         len(shifts),
		}).Info("compute attendance time from their log and shift")
		stats.totalArchivedDriversProceeded += 1
		stats.totalArchivedAttendanceLogProceeded += len(driver.AttendanceLogs)

		// (2.3) Compute attendance time from attendance logs and shift
		att := model.GetShiftAttendanceTime(driver.AttendanceLogs, shifts)

		// (2.4) Convert attendance time to AttendanceStats struct
		atds := t.mappingToAttendanceStats(from, att)

		// Require Mongo Transaction because dealing write with multiple documents
		_, err = t.traH.WithTxn(ctx, func(ctx context.Context) (interface{}, error) {
			// (2.5) Push AttendanceStats into `driver_order_info` collection
			err = t.doi.AddAttendanceStat(ctx, driver.DriverID, atds)
			if err != nil {
				logrus.WithError(err).WithFields(logrus.Fields{
					"driverID":        driver.DriverID,
					"attendanceStats": atds,
				}).Errorf("add attendance stat error: %v", err)

				return nil, err
			}

			// (2.6) Delete driver's attendance logs where time in between `from` and `to`
			err = t.dr.RemoveAttendanceLogInBetween(ctx, driver.DriverID, from, to)
			if err != nil {
				return nil, err
			}

			h := &model.AttendanceLogHistory{
				DriverID:       driver.DriverID,
				Shifts:         shifts,
				AttendanceLogs: driver.AttendanceLogs,
				RunAt:          runningDate,
				ExpiredAt:      timeutil.BangkokNow().Add(t.cfg.AttendanceLogHistoryExpired),
			}
			err = t.ath.Create(ctx, h)
			if err != nil {
				logrus.WithError(err).WithFields(logrus.Fields{
					"driverID": driver.DriverID,
				}).Errorf("add attendance log history err: %v", err)
				return nil, err
			}

			return nil, nil
		}, transaction.WithLabel("ATTRLogArchiveTask.Execute"))
		if err != nil {
			result.AddFail(driver.DriverID)
			continue
		}

		result.AddSuccess(driver.DriverID)
	}

	stats.totalDriversProceeded = len(drivers)

	logrus.WithFields(logrus.Fields{
		"params":                                        params,
		"stats_total_drivers_proceeded":                 stats.totalDriversProceeded,
		"stats_total_archived_drivers_proceeded":        stats.totalArchivedDriversProceeded,
		"stats_total_expired_drivers_proceeded":         stats.totalExpiredDriversProceeded,
		"stats_total_attendance_log_proceeded":          stats.totalAttendanceLogProceeded,
		"stats_total_archived_attendance_log_proceeded": stats.totalArchivedAttendanceLogProceeded,
		"stats_total_expired_attendance_log_proceeded":  stats.totalExpiredAttendanceLogProceeded,
	}).Info("attr log job done")

	return *result, nil
}

func (t *ATTRLogArchiveTask) parseDate(date string) (time.Time, error) {
	if date == "yesterday" {
		return timeutil.BangkokNow().Add(-1 * 24 * time.Hour), nil
	}

	if date == "today" {
		return timeutil.BangkokNow(), nil
	}

	return time.Parse("2/1/2006", date)
}

func (t *ATTRLogArchiveTask) mappingToAttendanceStats(effectiveDate time.Time, atdts []model.AttendanceTimeItem) model.AttendanceStat {
	var att []model.AttendanceTime
	for _, atdt := range atdts {
		att = append(att, model.AttendanceTime{
			ShiftId:   atdt.ShiftId,
			Actual:    atdt.Actual,
			ShiftTime: atdt.ShiftTime,
		})
	}
	return model.AttendanceStat{
		timeutil.ToYYYYMMDD(effectiveDate): att,
	}
}
