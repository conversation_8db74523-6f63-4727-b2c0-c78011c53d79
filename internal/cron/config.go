package cron

import (
	"time"

	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type IncentiveConfig struct {
	RunningDate    string  `envconfig:"RUNNING_DATE"  default:"yesterday" required:"true"`
	WithHoldingTax float64 `envconfig:"ORDER_MINIMUM_CREDIT_WITH_HOLDING_TAX"`
	BatchSize      int     `envconfig:"INCENTIVE_BATCH_SIZE" default:"500"`
}

func ProvideIncentiveConfig() (cfg IncentiveConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

// TodayEarningConfig empty driver id to run all driver
type TodayEarningConfig struct {
	DriverIds []string `envconfig:"DRIVER_IDS"  default:""`
}

func ProvideTodayEarningConfig() (cfg TodayEarningConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type ATTRLogArchiveConfig struct {
	AttendanceLogHistoryExpired time.Duration `envconfig:"ATTENDANCE_LOG_HISTORY_EXPIRED"  default:"2208h"` // 92 * 24
}

func ProvideATTRLogArchiveConfig() (cfg ATTRLogArchiveConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type CronInstallmentConfig struct {
	InstallmentWeekdayCutOff string `envconfig:"INSTALLMENT_WEEKDAY_CUT_OFF" default:"Monday"`
	InstallmentDPDCutOff     int    `envconfig:"INSTALLMENT_DPD_CUT_OFF"  default:"31"`
	// Ignore cut off for batch 1
	InstallmentCutOffIgnoreDate      string `envconfig:"INSTALLMENT_CUT_OFF_IGNORE_DATE" default:"2022-08-09"`
	EnableInventoryManagementService bool   `envconfig:"ENABLE_IMS"  default:"false"`
	CronInstallmentStartTime         string `envconfig:"EGS_INSTALLMENT_CUTOFF_TIME" default:"20:30"`
}

func ProvideCronInstallmentConfig() (cfg CronInstallmentConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type BNPLSOAFileTaskConfig struct {
	VosPath        string `envconfig:"BNPL_SOA_VOS_PATH" default:"bnpl_soa"`
	WorkerPoolSize int    `envconfig:"BNPL_SOA_WORKER_POOL_SIZE" default:"10"`
}

func ProvideBNPLSOAFileTaskConfig() (cfg BNPLSOAFileTaskConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type DailyIncomeConfig struct {
	DailyIncomeBatchSize      int `envconfig:"DAILY_INCOME_BATCH_SIZE"  default:"100"`
	DailyIncomeWorkerPoolSize int `envconfig:"DAILY_INCOME_WORKER_POOL_SIZE"  default:"50"`
}

func ProvideDailyIncomeConfig() (cfg DailyIncomeConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type ConvertCoinToCashConfig struct {
	ConvertDayOffset int     `envconfig:"CONVERT_DAY_OFFSET"  default:"-1"`
	WithHoldingTax   float64 `envconfig:"ORDER_MINIMUM_CREDIT_WITH_HOLDING_TAX"`
}

func ProvideConvertCoinToCashConfig() (cfg ConvertCoinToCashConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type CronEffectBanTimeConfig struct {
	EffectBanTimeWorkerSize int `envconfig:"EFFECT_BAN_TIME_WORKER_SIZE" default:"50"`
}

func ProvideCronEffectBanTimeConfig() (cfg CronEffectBanTimeConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type OrderExpireConfig struct {
	OrderBatchSize   int `envconfig:"ORDER_EXPIRE_BATCH_SIZE" default:"50"`
	ProcessBatchSize int `envconfig:"ORDER_EXPIRE_PROCESS_BATCH_SIZE" default:"10"`

	// ExpireContingencyDuration is the duration where order needs to be expired for more than this duration to be processed
	// This is used to prevent the race conditions that could occur when an order is assigned at the last minute
	ExpireContingencyDuration time.Duration `envconfig:"EXPIRE_CONTINGENCY_DURATION" default:"45s"`
}

func ProvideOrderExpireConfig() (cfg OrderExpireConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type CitiItemizeReportTaskConfig struct {
	SSHKeyFile             string `envconfig:"CITI_ITEMIZE_REPORT_SSH_FILE"`
	UserName               string `envconfig:"CITI_ITEMIZE_REPORT_USERNAME"`
	Addr                   string `envconfig:"CITI_ITEMIZE_REPORT_SFTP_ADDR"`
	SFTPFolder             string `envconfig:"CITI_ITEMIZE_REPORT_FOLDER"`
	VOSFolder              string `envconfig:"CITI_ITEMIZE_REPORT_VOS_FOLDER" default:"citi_itemize_report"`
	CitiItemizeSuccessCode string `envconfig:"CITI_ITEMIZE_SUCCESS_CODE" default:"ACCC"`
	InsertDbBatchSize      int    `envconfig:"CITI_ITEMIZE_INSERT_DB_BATCH_SIZE" default:"1000"`
}

func ProvideCitiItemizeReportTaskConfig() (cfg CitiItemizeReportTaskConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type CitiDailyReconcileTaskConfig struct {
	VOSReportFolder     string `envconfig:"CITI_DAILY_RECONCILE_VOS_REPORT_FOLDER" default:"citi_itemize_report"`
	PeriodOffsetMinutes int    `envconfig:"CITI_DAILY_RECONCILE_PERIOD_OFFSET_MINUTES" default:"5"`
	MaxRetries          int    `envconfig:"CITI_DAILY_RECONCILE_MAX_RETRIES" default:"3"`

	ReportEmailFrom     string   `envconfig:"CITI_DAILY_RECONCILE_REPORT_EMAIL_FROM"`
	ReportEmailPassword string   `envconfig:"CITI_DAILY_RECONCILE_REPORT_EMAIL_PASSWORD"`
	ReportEmailTos      []string `envconfig:"CITI_DAILY_RECONCILE_REPORT_EMAIL_TOS"`
}

func ProvideCitiDailyReconcileTaskConfig() (cfg CitiDailyReconcileTaskConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type CitiInquiryTransactionConfig struct {
	CitiInquiryHost                             string                    `envconfig:"CITI_INQUIRY_HOST"`
	CitiInquiryPath                             string                    `envconfig:"CITI_INQUIRY_PATH"`
	CitiInquiryClientID                         string                    `envconfig:"CITI_INQUIRY_CLIENT_ID"`
	CitiInquirySecret                           string                    `envconfig:"CITI_INQUIRY_SECRET"`
	CitiInquiryLimit                            int                       `envconfig:"CITI_INQUIRY_LIMIT" default:"100"`
	CitiSigningCert                             string                    `envconfig:"CITI_INQUIRY_SIGNING_CERTIFICATE_FILE"`
	LMWNPrivateKeyFile                          string                    `envconfig:"CITI_INQUIRY_LMWN_PRIVATE_KEY_FILE"`
	CitiCreditorAccount                         string                    `envconfig:"CITI_INQUIRY_LMWN_CITI_CREDITOR_ACCOUNT"`
	CitiEncKeyCertFile                          string                    `envconfig:"CITI_INQUIRY_ENCRYPTION_CERTIFICATE_FILE"`
	CitiInquiryBackTime                         int                       `envconfig:"CITI_INQUIRY_BACK_TIME" default:"31"`
	CitiInquiryProcessMissingTransactionEnabled bool                      `envconfig:"CITI_INQUIRY_PROCESS_MISSING_TRANSACTION_ENABLED" default:"false"`
	CitiInquirySlackNotificationEnabled         bool                      `envconfig:"CITI_INQUIRY_SLACK_NOTIFICATION_ENABLED" default:"true"`
	CitiInquirySlackCondition                   CitiInquirySlackCondition `envconfig:"CITI_INQUIRY_SLACK_CONDITION_ENABLED" default:"FOUND_TRANSACTION_ONLY"`
	CitiInquiryTimeout                          time.Duration             `envconfig:"CITI_INQUIRY_TIMEOUT"  default:"60s"`
	CitiInquiryIgnoreDebtorAccounts             types.StringSet           `envconfig:"CITI_INQUIRY_IGNORE_DEBTOR_ACCOUNTS" default:"-"`
	CitiInquiryIgnoreCreditorAccounts           types.StringSet           `envconfig:"CITI_INQUIRY_IGNORE_CREDITOR_ACCOUNTS" default:"-"`
}

type CitiInquirySlackCondition string

const (
	ShowAllTransactionCondition       CitiInquirySlackCondition = "ALL"
	ShowFoundTransactionOnlyCondition CitiInquirySlackCondition = "FOUND_TRANSACTION_ONLY"
)

func ProvideCitiInquiryCheckConfig() (cfg CitiInquiryTransactionConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type CronMigrateDriverMIDConfig struct {
	LINEChannelID             string  `envconfig:"LINE_CHANNEL_ID" default:""`
	LINEChannelSecret         string  `envconfig:"LINE_CHANNEL_SECRET" default:""`
	RequestSizeLimitPerSecond int     `envconfig:"REQUEST_SIZE_LIMIT_PER_SECOND"  default:"10"`
	BulkMIDSize               int     `envconfig:"BULK_MID_SIZE" default:"100"`
	RetryCount                int     `envconfig:"RETRY_COUNT"  default:"3"`
	ExitTime                  string  `envconfig:"EXIT_TIME"`
	MaxRun                    int     `envconfig:"MAX_RUN"`
	RateLimitDuration         float32 `envconfig:"RATE_LIMIT_DURATION" default:"1"`
}

func ProvideCronMigrateDriverMIDConfigConfig() (cfg CronMigrateDriverMIDConfig) {
	envconfig.MustProcess("MID_MIGRATOR", &cfg)
	return
}
