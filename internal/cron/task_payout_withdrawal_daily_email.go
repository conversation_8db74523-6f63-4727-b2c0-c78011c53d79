//go:generate mockgen -source=./task_tawi50_generator.go -destination=./mock_task_tawi50_generator.go -package=cron

package cron

import (
	"context"
	"fmt"

	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type PayoutWithdrawalDailyEmailTask struct {
	paymentService payment.PaymentService
	cfg            config.PaymentConfig
}

func ProvidePayoutWithdrawalDailyEmailTask(paymentService payment.PaymentService, cfg config.PaymentConfig) *PayoutWithdrawalDailyEmailTask {
	return &PayoutWithdrawalDailyEmailTask{paymentService: paymentService, cfg: cfg}
}

func (pw *PayoutWithdrawalDailyEmailTask) Execute(ctx context.Context, _ ...interface{}) (Result, error) {
	result := NewResult()

	if !pw.cfg.DailyEmailEnabled {
		logrus.Info("Payout Withdrawal Daily Email Disabled")
		return *result, nil
	}

	yesterday := timeutil.BangkokNow().AddDate(0, 0, -1)
	request := payment.ListTransactionReq{
		RequestedBy: pw.cfg.RequestedBy,
		From:        timeutil.DateTruncate(yesterday),
		To:          timeutil.DateCeiling(yesterday),
	}

	if err := pw.paymentService.ReportWithdrawResult(ctx, request); err != nil {
		msg := fmt.Sprintf("Payout Withdrawal Daily Email Fail: %v", err)
		logrus.Error(msg)
		result.AddFail(msg)
	} else {
		msg := "Payout Withdrawal Daily Email Success"
		logrus.Info(msg)
		result.AddSuccess(msg)
	}

	return *result, nil
}

func (pw *PayoutWithdrawalDailyEmailTask) Name() string {
	return "PayoutWithdrawalDailyEmailTask"
}
